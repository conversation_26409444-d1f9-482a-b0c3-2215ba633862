# Subscription and Billing System

This document provides a comprehensive overview of the subscription and billing system implemented in RapidMarkt.

## Overview

The subscription and billing system provides:
- Multiple subscription plans (Free, Starter, Professional, Enterprise)
- Usage tracking and limit enforcement
- Stripe integration for payment processing
- Webhook handling for real-time updates
- Background job processing for billing operations

## Architecture

### Models

#### Subscription
- **File**: `app/models/subscription.rb`
- **Purpose**: Manages subscription plans, Stripe integration, and billing cycles
- **Key Methods**:
  - `free_plan?`, `paid_plan?` - Plan type checks
  - `can_upgrade_to?`, `can_downgrade_to?` - Plan change validation
  - `cancel_at_period_end!`, `reactivate!` - Subscription management
  - `sync_with_stripe!` - Stripe synchronization

#### SubscriptionUsage
- **File**: `app/models/subscription_usage.rb`
- **Purpose**: Tracks usage metrics for billing periods
- **Key Methods**:
  - `current_for_account` - Get current usage record
  - `refresh_usage!` - Update usage from actual data
  - `approaching_limit?`, `over_limit?` - Limit checking
  - `usage_percentage_for` - Calculate usage percentages

#### Account (Enhanced)
- **File**: `app/models/account.rb`
- **Purpose**: Extended with billing and usage management
- **Key Methods**:
  - `ensure_stripe_customer!` - Stripe customer management
  - `current_usage`, `refresh_usage!` - Usage tracking
  - `can_add_contact?`, `can_send_campaign?` - Permission checks
  - `plan_config` - Get current plan configuration

### Services

#### SubscriptionService
- **File**: `app/services/subscription_service.rb`
- **Purpose**: Handles subscription operations with Stripe
- **Key Methods**:
  - `create_subscription` - Create new paid subscription
  - `change_plan` - Upgrade/downgrade plans
  - `cancel_subscription`, `reactivate_subscription` - Lifecycle management
  - `update_payment_method` - Payment method updates

#### UsageTrackingService
- **File**: `app/services/usage_tracking_service.rb`
- **Purpose**: Manages usage monitoring and limit enforcement
- **Key Methods**:
  - `can_add_contact?`, `can_send_campaign?` - Permission checks
  - `track_contact_added!`, `track_campaign_sent!` - Usage tracking
  - `refresh_usage!` - Sync with actual data
  - `over_limits?`, `approaching_limits?` - Limit monitoring

### Controllers

#### SubscriptionsController
- **File**: `app/controllers/subscriptions_controller.rb`
- **Purpose**: API endpoints for subscription management
- **Endpoints**:
  - `GET /subscription` - Show current subscription
  - `POST /subscription` - Create subscription
  - `PATCH /subscription/change_plan` - Change plan
  - `PATCH /subscription/cancel` - Cancel subscription
  - `GET /subscription/usage` - Get usage statistics

#### BillingController
- **File**: `app/controllers/billing_controller.rb`
- **Purpose**: Billing and payment management
- **Endpoints**:
  - `GET /billing` - Billing overview
  - `GET /billing/invoices` - List invoices
  - `GET /billing/payment_methods` - List payment methods
  - `POST /billing/create_portal_session` - Stripe customer portal

#### StripeWebhooksController
- **File**: `app/controllers/stripe_webhooks_controller.rb`
- **Purpose**: Handle Stripe webhook events
- **Events Handled**:
  - Subscription lifecycle events
  - Payment success/failure
  - Customer management
  - Payment method changes

### Background Jobs

#### UsageTrackingJob
- **File**: `app/jobs/usage_tracking_job.rb`
- **Purpose**: Background usage tracking operations
- **Actions**:
  - `refresh_usage` - Update usage metrics
  - `check_limits` - Monitor limit violations
  - `send_usage_alert` - Send notifications

#### StripeWebhookJob
- **File**: `app/jobs/stripe_webhook_job.rb`
- **Purpose**: Process Stripe webhooks asynchronously
- **Features**:
  - Subscription synchronization
  - Payment processing
  - Customer management

#### BillingCycleJob
- **File**: `app/jobs/billing_cycle_job.rb`
- **Purpose**: Daily billing operations
- **Operations**:
  - Billing reminders
  - Usage resets
  - Failed payment handling
  - Data cleanup

### Views

#### Subscription Management
- **File**: `app/views/subscriptions/show.html.erb`
- **Features**:
  - Current plan display
  - Usage statistics
  - Plan comparison
  - Payment method management

#### Billing Overview
- **File**: `app/views/billing/index.html.erb`
- **Features**:
  - Subscription overview
  - Invoice history
  - Payment methods
  - Quick actions

## Configuration

### Stripe Configuration
- **File**: `config/stripe.yml`
- **Purpose**: Plan definitions and billing settings
- **Sections**:
  - Plan configurations with limits and features
  - Webhook event definitions
  - Usage tracking settings
  - Billing cycle configuration

### Initializer
- **File**: `config/initializers/stripe.rb`
- **Purpose**: Stripe setup and helper methods
- **Features**:
  - API key configuration
  - Plan helper methods
  - Configuration validation

### Routes
- **File**: `config/routes_stripe.rb`
- **Purpose**: Subscription and billing routes
- **Included in**: `config/routes.rb`

## Database Schema

### Migrations

#### CreateSubscriptionUsages
- **File**: `db/migrate/*_create_subscription_usages.rb`
- **Purpose**: Usage tracking table
- **Columns**:
  - `account_id` - Foreign key to accounts
  - `period_start`, `period_end` - Billing period
  - Usage counters (contacts, campaigns, templates, storage)
  - Timestamps and indexes

#### AddStripeCustomerIdToAccounts
- **File**: `db/migrate/*_add_stripe_customer_id_to_accounts.rb`
- **Purpose**: Stripe integration for accounts
- **Columns**:
  - `stripe_customer_id` - Stripe customer reference
  - `usage_reset_date` - Billing cycle tracking

## Setup Instructions

### 1. Environment Configuration

```bash
# Add to Rails credentials
rails credentials:edit

# Add Stripe keys:
stripe:
  publishable_key: pk_test_...
  secret_key: sk_test_...
  webhook_secret: whsec_...
```

### 2. Database Migration

```bash
rails db:migrate
```

### 3. Stripe Setup

1. Create products and prices in Stripe Dashboard
2. Update `config/stripe.yml` with actual price IDs
3. Configure webhook endpoint: `/stripe/webhooks`
4. Set webhook events (see `config/stripe.yml`)

### 4. Background Jobs

```bash
# For development
bin/rails jobs:work

# For production (with cron)
# Add to crontab:
# 0 2 * * * cd /path/to/app && bin/rails runner "BillingCycleJob.perform_later"
```

## Usage Examples

### Creating a Subscription

```ruby
# In controller or service
service = SubscriptionService.new(account)
result = service.create_subscription(
  plan: 'starter',
  payment_method_id: 'pm_1234567890'
)

if result[:success]
  # Subscription created successfully
  subscription = result[:subscription]
else
  # Handle error
  error = result[:error]
end
```

### Checking Usage Limits

```ruby
# Check if account can perform action
if account.can_add_contact?
  # Add contact
  contact = account.contacts.create!(contact_params)
  
  # Track usage
  UsageTrackingService.new(account).track_contact_added!
else
  # Show upgrade prompt
  redirect_to subscription_path, alert: 'Contact limit reached'
end
```

### Usage Tracking

```ruby
# Get current usage statistics
usage_service = UsageTrackingService.new(account)
stats = usage_service.usage_stats

# Check specific limits
if usage_service.approaching_limits?
  # Show warning
  limits = usage_service.approaching_limits
end

# Refresh usage from actual data
usage_service.refresh_usage!
```

## API Endpoints

### Subscription Management

```
GET    /subscription              # Show current subscription
POST   /subscription              # Create subscription
PATCH  /subscription/change_plan  # Change plan
PATCH  /subscription/cancel       # Cancel subscription
PATCH  /subscription/reactivate   # Reactivate subscription
GET    /subscription/usage        # Get usage statistics
POST   /subscription/refresh_usage # Refresh usage data
```

### Billing Management

```
GET    /billing                   # Billing overview
GET    /billing/invoices          # List invoices
GET    /billing/payment_methods   # List payment methods
POST   /billing/payment_methods   # Add payment method
DELETE /billing/payment_methods/:id # Remove payment method
POST   /billing/create_portal_session # Stripe customer portal
```

### Webhooks

```
POST   /stripe/webhooks           # Stripe webhook endpoint
```

## Monitoring and Alerts

### Usage Alerts
- 80% usage warning
- 95% usage critical alert
- Limit exceeded notifications

### Billing Alerts
- Payment failure notifications
- Billing reminders (7, 3, 1 days before)
- Subscription cancellation alerts

### Logging
- All subscription changes
- Usage tracking events
- Payment processing
- Webhook events
- Error conditions

## Security Considerations

### Webhook Security
- Stripe signature verification
- Idempotency handling
- Rate limiting

### Data Protection
- Encrypted credentials
- Secure API key storage
- PCI compliance considerations

### Access Control
- Account-based permissions
- Usage limit enforcement
- Plan-based feature access

## Troubleshooting

### Common Issues

1. **Webhook failures**
   - Check webhook signature
   - Verify endpoint URL
   - Review webhook logs in Stripe

2. **Usage tracking discrepancies**
   - Run `refresh_usage!` method
   - Check for data inconsistencies
   - Review usage calculation logic

3. **Payment failures**
   - Check Stripe customer status
   - Verify payment method validity
   - Review subscription status

### Debugging

```ruby
# Check subscription status
account.subscription.sync_with_stripe!

# Refresh usage data
account.refresh_usage!

# Check plan configuration
StripePlanHelper.plan_config('starter')

# Validate limits
UsageTrackingService.new(account).usage_stats
```

## Testing

### Test Data Setup

```ruby
# Create test account with subscription
account = Account.create!(name: 'Test Account')
subscription = account.create_subscription!(
  plan: 'starter',
  status: 'active'
)

# Create usage record
usage = account.subscription_usages.create!(
  period_start: Time.current.beginning_of_month,
  period_end: Time.current.end_of_month,
  contacts_count: 50,
  campaigns_sent_count: 5
)
```

### Webhook Testing

```bash
# Use Stripe CLI for webhook testing
stripe listen --forward-to localhost:3000/stripe/webhooks
stripe trigger customer.subscription.created
```

## Performance Considerations

### Database Optimization
- Indexes on frequently queried columns
- Periodic cleanup of old usage records
- Efficient usage calculation queries

### Background Processing
- Async webhook processing
- Batched operations
- Rate limiting for Stripe API calls

### Caching
- Plan configuration caching
- Usage statistics caching
- Stripe customer data caching

## Future Enhancements

### Planned Features
- Usage-based billing
- Annual subscription discounts
- Team member management
- Advanced analytics
- Custom plan creation

### Integration Opportunities
- Third-party payment processors
- Accounting system integration
- CRM integration
- Advanced reporting tools