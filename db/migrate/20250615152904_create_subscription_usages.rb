class CreateSubscriptionUsages < ActiveRecord::Migration[8.0]
  def change
    create_table :subscription_usages do |t|
      t.references :account, null: false, foreign_key: true
      t.datetime :period_start, null: false
      t.datetime :period_end, null: false
      t.integer :contacts_count, default: 0
      t.integer :campaigns_sent_count, default: 0
      t.integer :templates_count, default: 0
      t.bigint :storage_used, default: 0

      t.timestamps
    end
    
    add_index :subscription_usages, [:account_id, :period_start], unique: true
    add_index :subscription_usages, :period_start
    add_index :subscription_usages, :period_end
  end
end
