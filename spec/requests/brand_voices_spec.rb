require 'rails_helper'

RSpec.describe "BrandVoices", type: :request do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }
  let(:brand_voice) { create(:brand_voice, account: account) }

  before do
    sign_in user
  end

  describe "GET /brand_voices" do
    it "returns http success" do
      get brand_voices_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /brand_voices/:id" do
    it "returns http success" do
      get brand_voice_path(brand_voice)
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /brand_voices/new" do
    it "returns http success" do
      get new_brand_voice_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "POST /brand_voices" do
    it "creates a brand voice and redirects" do
      brand_voice_params = {
        name: "Test Voice",
        tone: "professional",
        description: "A test brand voice"
      }
      
      post brand_voices_path, params: { brand_voice: brand_voice_params }
      expect(response).to have_http_status(:redirect)
      expect(BrandVoice.last.name).to eq("Test Voice")
    end
  end

  describe "GET /brand_voices/:id/edit" do
    it "returns http success" do
      get edit_brand_voice_path(brand_voice)
      expect(response).to have_http_status(:success)
    end
  end

  describe "PATCH /brand_voices/:id" do
    it "updates the brand voice and redirects" do
      patch brand_voice_path(brand_voice), params: { brand_voice: { name: "Updated Voice" } }
      expect(response).to have_http_status(:redirect)
      expect(brand_voice.reload.name).to eq("Updated Voice")
    end
  end

  describe "DELETE /brand_voices/:id" do
    it "destroys the brand voice and redirects" do
      delete brand_voice_path(brand_voice)
      expect(response).to have_http_status(:redirect)
      expect(BrandVoice.exists?(brand_voice.id)).to be_falsey
    end
  end
end
