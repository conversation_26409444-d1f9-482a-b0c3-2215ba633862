<% content_for :title, "Billing" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Billing</h1>
    <p class="mt-2 text-gray-600">View your billing history and manage payment methods</p>
  </div>

  <!-- Current Subscription Overview -->
  <div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900">Current Subscription</h2>
    </div>
    <div class="px-6 py-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <h3 class="text-sm font-medium text-gray-500">Plan</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900 capitalize"><%= @current_plan %></p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Monthly Cost</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">
            <%= @available_plans[@current_plan][:price] > 0 ? "$#{@available_plans[@current_plan][:price] / 100}" : "Free" %>
          </p>
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Next Billing Date</h3>
          <p class="mt-1 text-lg font-semibold text-gray-900">
            <% if @subscription&.active? && @subscription.current_period_end %>
              <%= @subscription.current_period_end.strftime("%B %d, %Y") %>
            <% else %>
              N/A
            <% end %>
          </p>
        </div>
      </div>
      
      <% if @subscription&.cancel_at_period_end? %>
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Subscription Scheduled for Cancellation</h3>
              <p class="mt-1 text-sm text-yellow-700">
                Your subscription will be canceled on <%= @subscription.current_period_end.strftime("%B %d, %Y") %>.
                You can reactivate it anytime before then.
              </p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900">Quick Actions</h2>
    </div>
    <div class="px-6 py-4">
      <div class="flex flex-wrap gap-4">
        <%= link_to subscription_path, class: "bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded" do %>
          Manage Subscription
        <% end %>
        
        <% if @subscription&.active? && @current_plan != 'free' %>
          <%= form_with url: billing_path + "/create_portal_session", method: :post, local: true, class: "inline" do |form| %>
            <%= form.submit "Customer Portal", 
                class: "bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded" %>
          <% end %>
        <% end %>
        
        <% if @subscription&.active? && @subscription.current_period_end %>
          <%= link_to billing_upcoming_invoice_path, class: "bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded" do %>
            View Upcoming Invoice
          <% end %>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Recent Invoices -->
  <div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-medium text-gray-900">Recent Invoices</h2>
        <%= link_to billing_invoices_path, class: "text-sm text-blue-600 hover:text-blue-800" do %>
          View All Invoices
        <% end %>
      </div>
    </div>
    <div class="px-6 py-4">
      <% if @invoices.any? %>
        <div class="overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @invoices.each do |invoice| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    #<%= invoice.number || invoice.id %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= Time.at(invoice.created).strftime("%B %d, %Y") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    $<%= sprintf("%.2f", invoice.total / 100.0) %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% case invoice.status %>
                    <% when 'paid' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Paid
                      </span>
                    <% when 'open' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending
                      </span>
                    <% when 'void' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Void
                      </span>
                    <% when 'uncollectible' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Failed
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <%= invoice.status.capitalize %>
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex space-x-2">
                      <%= link_to billing_invoice_path(invoice.id), class: "text-blue-600 hover:text-blue-800" do %>
                        View
                      <% end %>
                      <% if invoice.invoice_pdf %>
                        <%= link_to invoice.invoice_pdf, target: "_blank", class: "text-blue-600 hover:text-blue-800" do %>
                          PDF
                        <% end %>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
          <p class="mt-1 text-sm text-gray-500">You don't have any invoices yet.</p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Payment Methods -->
  <div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-medium text-gray-900">Payment Methods</h2>
        <%= link_to billing_payment_methods_path, class: "text-sm text-blue-600 hover:text-blue-800" do %>
          Manage Payment Methods
        <% end %>
      </div>
    </div>
    <div class="px-6 py-4">
      <% if @payment_methods.any? %>
        <div class="space-y-4">
          <% @payment_methods.first(2).each do |payment_method| %>
            <div class="flex items-center justify-between p-4 border rounded-lg <%= payment_method.id == @default_payment_method ? 'border-blue-500 bg-blue-50' : 'border-gray-200' %>">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">**** **** **** <%= payment_method.card.last4 %></p>
                  <p class="text-sm text-gray-500"><%= payment_method.card.brand.upcase %> • Expires <%= payment_method.card.exp_month %>/<%= payment_method.card.exp_year %></p>
                </div>
                <% if payment_method.id == @default_payment_method %>
                  <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Default
                  </span>
                <% end %>
              </div>
            </div>
          <% end %>
          
          <% if @payment_methods.length > 2 %>
            <p class="text-sm text-gray-500 text-center">
              And <%= @payment_methods.length - 2 %> more payment method<%= @payment_methods.length - 2 == 1 ? '' : 's' %>
            </p>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No payment methods</h3>
          <p class="mt-1 text-sm text-gray-500">Add a payment method to manage your subscription.</p>
          <div class="mt-4">
            <%= link_to billing_payment_methods_path, class: "bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded" do %>
              Add Payment Method
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>