<div class="min-h-full">
  <!-- Page header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Email Templates
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Create and manage reusable email templates for your campaigns.
      </p>
    </div>
    <div class="mt-4 flex space-x-3 md:mt-0 md:ml-4">
      <%= link_to builder_templates_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12z" clip-rule="evenodd" />
        </svg>
        Template Builder
      <% end %>
      
      <%= link_to new_template_path, 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Simple Editor
      <% end %>
    </div>
  </div>

  <!-- Search and filters -->
  <div class="mt-8">
    <%= form_with url: templates_path, method: :get, local: true, class: "space-y-4 sm:space-y-0 sm:flex sm:items-center sm:space-x-4" do |form| %>
      <div class="flex-1">
        <%= form.text_field :search, 
            value: params[:search],
            placeholder: "Search templates by name or subject...",
            class: "block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
      </div>
      <div>
        <%= form.submit "Search", 
            class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      </div>
      <% if params[:search].present? %>
        <div>
          <%= link_to templates_path, 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            Clear
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>

  <!-- Templates grid -->
  <div class="mt-8">
    <% if @templates.any? %>
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <% @templates.each do |template| %>
          <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
            <div class="p-6">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-medium text-gray-900 truncate">
                    <%= link_to template.name, template_path(template), class: "hover:text-indigo-600" %>
                  </h3>
                  <p class="mt-1 text-sm text-gray-500 truncate">
                    <%= template.subject %>
                  </p>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="relative inline-block text-left" data-controller="dropdown">
                    <button type="button"
                            class="bg-white rounded-full flex items-center text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 p-2"
                            data-action="click->dropdown#toggle"
                            aria-label="Template options">
                      <span class="sr-only">Open options</span>
                      <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                      </svg>
                    </button>
                    <div class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden" 
                         data-dropdown-target="menu">
                      <div class="py-1">
                        <%= link_to template_path(template), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                          </svg>
                          View
                        <% end %>
                        <%= link_to builder_templates_path(template_id: template.id), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12z" clip-rule="evenodd" />
                          </svg>
                          Edit in Builder
                        <% end %>
                        <%= link_to edit_template_path(template), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                          Simple Edit
                        <% end %>
                        <%= link_to preview_template_path(template), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                            target: "_blank" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12z" clip-rule="evenodd" />
                          </svg>
                          Preview
                        <% end %>
                        <%= link_to duplicate_template_path(template), 
                            data: { turbo_method: :post },
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                            <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                          </svg>
                          Duplicate
                        <% end %>
                        <div class="border-t border-gray-100"></div>
                        <%= link_to template_path(template), 
                            data: { turbo_method: :delete, turbo_confirm: 'Are you sure you want to delete this template?' },
                            class: "block px-4 py-2 text-sm text-red-700 hover:bg-red-50" do %>
                          <svg class="mr-3 h-5 w-5 text-red-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l1.293-1.293z" clip-rule="evenodd" />
                          </svg>
                          Delete
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="mt-4">
                <div class="text-sm text-gray-500">
                  Created <%= time_ago_in_words(template.created_at) %> ago
                </div>
                <% if template.body.present? %>
                  <div class="mt-2 text-sm text-gray-600 line-clamp-3">
                    <%= truncate(strip_tags(template.body), length: 120) %>
                  </div>
                <% end %>
              </div>
              
              <div class="mt-4 flex items-center justify-between">
                <div class="text-xs text-gray-500">
                  <% campaigns_count = template.campaigns.count %>
                  Used in <%= pluralize(campaigns_count, 'campaign') %>
                </div>
                <div class="flex space-x-2">
                  <%= link_to template_path(template), 
                      class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                    View
                  <% end %>
                  <%= link_to edit_template_path(template), 
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                    Edit
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <!-- Pagination -->
      <%= paginate @templates if defined?(Kaminari) %>
    <% else %>
      <!-- Empty state -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">
          <% if params[:search].present? %>
            No templates found
          <% else %>
            No email templates
          <% end %>
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          <% if params[:search].present? %>
            Try adjusting your search terms or create a new template.
          <% else %>
            Get started by creating your first email template.
          <% end %>
        </p>
        <div class="mt-6">
          <%= link_to new_template_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            New Template
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>