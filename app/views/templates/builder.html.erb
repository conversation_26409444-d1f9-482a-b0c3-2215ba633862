<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
  <title>Enhanced Template Builder - RapidMarkt</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  
  <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
  <%= javascript_importmap_tags %>
  
  <!-- Enhanced Template Builder Styles -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-50: #eff6ff;
      --primary-100: #dbeafe;
      --primary-200: #bfdbfe;
      --primary-500: #3b82f6;
      --primary-600: #2563eb;
      --primary-700: #1d4ed8;
      --primary-900: #1e3a8a;
      
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
      
      --success-50: #ecfdf5;
      --success-500: #10b981;
      --warning-50: #fffbeb;
      --warning-500: #f59e0b;
      --error-50: #fef2f2;
      --error-500: #ef4444;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
      color: var(--gray-900);
      overflow: hidden;
      height: 100vh;
    }

    .builder-layout {
      display: grid;
      grid-template-columns: 300px 1fr 350px;
      grid-template-rows: 60px 1fr;
      height: 100vh;
      gap: 0;
    }

    .builder-header {
      grid-column: 1 / -1;
      background: white;
      border-bottom: 1px solid var(--gray-200);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      z-index: 50;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0;
    }

    .template-name-input {
      background: var(--gray-50);
      border: 1px solid var(--gray-200);
      border-radius: 8px;
      padding: 8px 12px;
      font-size: 14px;
      min-width: 200px;
      transition: all 0.2s;
    }

    .template-name-input:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.2s;
      cursor: pointer;
      border: none;
      white-space: nowrap;
    }

    .btn-primary {
      background: var(--primary-600);
      color: white;
    }

    .btn-primary:hover {
      background: var(--primary-700);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: white;
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
    }

    .btn-secondary:hover {
      background: var(--gray-50);
      border-color: var(--gray-400);
    }

    .btn-success {
      background: var(--success-500);
      color: white;
    }

    .btn-success:hover {
      background: #059669;
      transform: translateY(-1px);
    }

    .sidebar {
      background: white;
      border-right: 1px solid var(--gray-200);
      overflow-y: auto;
      padding: 0;
      position: relative;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .sidebar-header {
      padding: 20px;
      border-bottom: 1px solid var(--gray-200);
      background: var(--gray-50);
    }

    .sidebar-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 8px 0;
    }

    .sidebar-subtitle {
      font-size: 14px;
      color: var(--gray-600);
      margin: 0;
    }

    .sidebar-content {
      padding: 0;
      flex: 1;
      overflow-y: auto;
    }

    .tab-nav {
      display: flex;
      border-bottom: 1px solid var(--gray-200);
    }

    .tab-btn {
      flex: 1;
      padding: 12px 16px;
      background: none;
      border: none;
      color: var(--gray-600);
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      border-bottom: 2px solid transparent;
    }

    .tab-btn.active {
      color: var(--primary-600);
      border-bottom-color: var(--primary-600);
      background: var(--primary-50);
    }

    .tab-btn:hover:not(.active) {
      color: var(--gray-900);
      background: var(--gray-50);
    }

    .tab-content {
      padding: 20px;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
    }

    .component-section {
      margin-bottom: 24px;
    }

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 12px 0;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .component-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .component-item {
      background: white;
      border: 2px solid var(--gray-200);
      border-radius: 12px;
      padding: 16px;
      cursor: grab;
      transition: all 0.3s ease;
      position: relative;
      text-align: center;
    }

    .component-item:hover {
      border-color: var(--primary-300);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .component-item:active {
      cursor: grabbing;
      transform: scale(0.98);
    }

    .component-icon {
      font-size: 24px;
      margin-bottom: 8px;
      display: block;
    }

    .component-name {
      font-size: 12px;
      font-weight: 500;
      color: var(--gray-700);
      margin: 0;
    }

    .canvas-area {
      background: var(--gray-100);
      position: relative;
      overflow: auto;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      padding: 24px;
      height: 100%;
    }

    .canvas-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
      min-height: calc(100vh - 160px);
      width: 100%;
      max-width: none;
      position: relative;
      transition: all 0.3s;
      flex: 1;
    }

    .canvas-container.drag-over {
      border: 2px dashed var(--primary-500);
      background: var(--primary-50);
    }

    .canvas-content {
      padding: 32px;
      width: 100%;
      height: 100%;
      position: relative;
      overflow: auto;
      box-sizing: border-box;
      min-height: calc(100vh - 160px);
    }

    .drop-zone {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 500px;
      width: 100%;
      border: 2px dashed var(--gray-300);
      border-radius: 12px;
      background: var(--gray-50);
      text-align: center;
      transition: all 0.3s ease;
      margin: 0;
      cursor: pointer;
      padding: 60px 20px;
    }

    .drop-zone.drag-over {
      border-color: var(--primary-500);
      background: var(--primary-50);
      color: var(--primary-700);
      transform: scale(1.02);
      border-style: solid;
    }

    .drop-zone-icon {
      font-size: 48px;
      color: var(--gray-400);
      margin-bottom: 16px;
    }

    .drop-zone-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 8px 0;
    }

    .drop-zone-subtitle {
      font-size: 14px;
      color: var(--gray-600);
      margin: 0;
    }

    .properties-panel {
      background: white;
      border-left: 1px solid var(--gray-200);
      overflow-y: auto;
      position: relative;
      height: 100%;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
    }

    .properties-header {
      padding: 20px;
      border-bottom: 1px solid var(--gray-200);
      background: var(--gray-50);
    }

    .properties-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 8px 0;
    }

    .properties-content {
      padding: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: 6px;
    }

    .form-input,
    .form-select,
    .form-textarea {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid var(--gray-300);
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.2s;
      background: white;
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }

    .color-picker {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .color-input {
      width: 40px;
      height: 32px;
      padding: 0;
      border: 1px solid var(--gray-300);
      border-radius: 6px;
      cursor: pointer;
    }

    .floating-toolbar {
      position: absolute;
      top: 20px;
      right: 20px;
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: 12px;
      padding: 8px;
      display: flex;
      gap: 4px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 10;
    }

    .toolbar-btn {
      width: 36px;
      height: 36px;
      border: none;
      border-radius: 8px;
      background: none;
      color: var(--gray-600);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
    }

    .toolbar-btn:hover {
      background: var(--gray-100);
      color: var(--gray-900);
    }

    .template-element {
      position: relative;
      margin: 16px 0;
      padding: 16px;
      border: 2px solid transparent;
      border-radius: 8px;
      transition: all 0.2s;
    }

    .template-element:hover {
      border-color: var(--primary-300);
      background: var(--primary-50);
    }

    .template-element.selected {
      border-color: var(--primary-500);
      background: var(--primary-50);
    }

    .element-controls {
      position: absolute;
      top: -12px;
      right: -12px;
      display: none;
      gap: 4px;
    }

    .template-element:hover .element-controls,
    .template-element.selected .element-controls {
      display: flex;
    }

    .control-btn {
      width: 24px;
      height: 24px;
      border: none;
      border-radius: 50%;
      background: var(--primary-600);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      transition: all 0.2s;
    }

    .control-btn:hover {
      background: var(--primary-700);
      transform: scale(1.1);
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 16px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      background: var(--success-500);
    }

    .notification.error {
      background: var(--error-500);
    }

    .notification.warning {
      background: var(--warning-500);
    }

    .loading-overlay {
      position: fixed;
      inset: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s;
    }

    .loading-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Enhanced drag and drop styles */
    body.dragging {
      cursor: grabbing !important;
    }

    body.dragging * {
      pointer-events: none;
    }

    body.dragging .canvas-container,
    body.dragging .canvas-content,
    body.dragging .drop-zone {
      pointer-events: auto;
    }

    .component-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    .component-item.dragging {
      opacity: 0.5;
      transform: scale(0.95) rotate(2deg);
    }

    .search-box {
      position: relative;
      margin-bottom: 16px;
    }

    .search-input {
      width: 100%;
      padding: 10px 12px 10px 36px;
      border: 1px solid var(--gray-300);
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.2s;
    }

    .search-input:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--gray-400);
      font-size: 16px;
    }

    .version-info {
      padding: 12px 16px;
      background: var(--gray-50);
      border-top: 1px solid var(--gray-200);
      font-size: 12px;
      color: var(--gray-600);
      text-align: center;
    }

    .responsive-preview {
      display: flex;
      gap: 8px;
      margin-bottom: 20px;
    }

    .device-btn {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid var(--gray-300);
      background: white;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .device-btn.active {
      background: var(--primary-600);
      color: white;
      border-color: var(--primary-600);
    }

    .device-btn:hover:not(.active) {
      background: var(--gray-50);
    }

    @media (max-width: 1024px) {
      .builder-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 60px auto 1fr auto;
      }
      
      .sidebar,
      .properties-panel {
        position: fixed;
        top: 60px;
        bottom: 0;
        width: 320px;
        z-index: 40;
        transform: translateX(-100%);
        transition: transform 0.3s;
      }
      
      .sidebar.open,
      .properties-panel.open {
        transform: translateX(0);
      }
      
      .properties-panel {
        right: 0;
        transform: translateX(100%);
      }
      
      .canvas-area {
        grid-column: 1;
        grid-row: 3;
      }
    }
  </style>
</head>
<body>
  <!-- Streamlined Campaign Builder Header -->
  <header class="builder-header">
    <div class="header-content">
      <!-- Left: Brand & Campaign Info -->
      <div class="header-left">
        <div class="brand-section">
          <h1 class="brand-title">RapidMarkt Studio</h1>
        </div>
        <div class="campaign-section">
          <input 
            type="text" 
            class="campaign-input" 
            placeholder="Campaign Name"
            value="<%= params[:name] || 'Untitled Campaign' %>"
            id="campaignName"
          >
          <select id="platformSelect" class="platform-select">
            <option value="email">📧 Email</option>
            <option value="social">📱 Social</option>
            <option value="web">🌐 Landing</option>
            <option value="tiktok">🎵 TikTok</option>
            <option value="youtube">📺 YouTube</option>
            <option value="instagram">📸 Instagram</option>
            <option value="linkedin">💼 LinkedIn</option>
          </select>
        </div>
      </div>
      
      <!-- Center: Device Preview -->
      <div class="header-center">
        <div class="device-preview">
          <button class="device-btn active" data-device="desktop" title="Desktop View">🖥️</button>
          <button class="device-btn" data-device="tablet" title="Tablet View">📱</button>
          <button class="device-btn" data-device="mobile" title="Mobile View">📱</button>
        </div>
      </div>
      
      <!-- Right: Actions -->
      <div class="header-right">
        <div class="quick-actions">
          <button class="action-btn undo" onclick="undoAction()" title="Undo">↶</button>
          <button class="action-btn redo" onclick="redoAction()" title="Redo">↷</button>
        </div>
        <div class="main-actions">
          <button class="btn btn-ai" onclick="generateWithAI()">✨ AI Generate</button>
          <button class="btn btn-preview" onclick="previewCampaign()">👁️ Preview</button>
          <button class="btn btn-save" onclick="saveAsDraft()">💾 Save</button>
          <button class="btn btn-publish" onclick="publishCampaign()">🚀 Publish</button>
        </div>
      </div>
    </div>
  </header>

  <div class="builder-main">
    <!-- Left Sidebar - Platform Components & AI Tools -->
    <aside class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <h2 class="sidebar-title">🎨 Design Studio</h2>
        <p class="sidebar-subtitle">Professional components for every platform</p>
      </div>
      
      <div class="sidebar-content">
        <div class="tab-nav">
          <button class="tab-btn active" onclick="switchTab('components')">
            <span class="tab-icon">🧩</span>
            <span class="tab-label">Components</span>
          </button>
          <button class="tab-btn" onclick="switchTab('ai')">
            <span class="tab-icon">🤖</span>
            <span class="tab-label">AI Studio</span>
          </button>
          <button class="tab-btn" onclick="switchTab('templates')">
            <span class="tab-icon">📚</span>
            <span class="tab-label">Templates</span>
          </button>
          <button class="tab-btn" onclick="switchTab('brand')">
            <span class="tab-icon">🎨</span>
            <span class="tab-label">Brand Kit</span>
          </button>
        </div>
        
        <!-- Components Tab -->
        <div id="components-tab" class="tab-pane active">
          <div class="tab-content">
            <div class="search-box">
              <input type="text" class="search-input" placeholder="Search components..." id="componentSearch">
              <span class="search-icon">🔍</span>
            </div>
            
            <!-- Platform Filter -->
            <div class="platform-filter">
              <button class="filter-btn active" data-platform="all">All Platforms</button>
              <button class="filter-btn" data-platform="email">📧 Email</button>
              <button class="filter-btn" data-platform="social">📱 Social</button>
              <button class="filter-btn" data-platform="web">🌐 Web</button>
            </div>
            
            <!-- Email Components -->
            <div class="component-section" data-platform="email">
              <h3 class="section-title">📧 Email Campaign Components</h3>
              <div class="component-grid">
                <div class="component-item premium" draggable="true" data-component="email-header">
                  <span class="component-icon">📄</span>
                  <p class="component-name">Email Header</p>
                  <span class="platform-badge">Email</span>
                </div>
                <div class="component-item" draggable="true" data-component="newsletter-block">
                  <span class="component-icon">📰</span>
                  <p class="component-name">Newsletter</p>
                  <span class="platform-badge">Email</span>
                </div>
                <div class="component-item premium" draggable="true" data-component="email-cta">
                  <span class="component-icon">📧</span>
                  <p class="component-name">Email CTA</p>
                  <span class="platform-badge">Email</span>
                </div>
                <div class="component-item" draggable="true" data-component="unsubscribe-footer">
                  <span class="component-icon">👋</span>
                  <p class="component-name">Footer</p>
                  <span class="platform-badge">Email</span>
                </div>
              </div>
            </div>
            
            <!-- Social Media Components -->
            <div class="component-section" data-platform="social">
              <h3 class="section-title">📱 Social Media Components</h3>
              <div class="component-grid">
                <div class="component-item premium" draggable="true" data-component="tiktok-video">
                  <span class="component-icon">🎵</span>
                  <p class="component-name">TikTok Video</p>
                  <span class="platform-badge tiktok">TikTok</span>
                </div>
                <div class="component-item premium" draggable="true" data-component="instagram-story">
                  <span class="component-icon">📸</span>
                  <p class="component-name">IG Story</p>
                  <span class="platform-badge instagram">Instagram</span>
                </div>
                <div class="component-item" draggable="true" data-component="youtube-thumbnail">
                  <span class="component-icon">📺</span>
                  <p class="component-name">YT Thumbnail</p>
                  <span class="platform-badge youtube">YouTube</span>
                </div>
                <div class="component-item premium" draggable="true" data-component="linkedin-post">
                  <span class="component-icon">💼</span>
                  <p class="component-name">LinkedIn Post</p>
                  <span class="platform-badge linkedin">LinkedIn</span>
                </div>
                <div class="component-item" draggable="true" data-component="facebook-ad">
                  <span class="component-icon">👥</span>
                  <p class="component-name">FB Ad</p>
                  <span class="platform-badge facebook">Facebook</span>
                </div>
                <div class="component-item premium" draggable="true" data-component="twitter-thread">
                  <span class="component-icon">🐦</span>
                  <p class="component-name">X Thread</p>
                  <span class="platform-badge twitter">X/Twitter</span>
                </div>
              </div>
            </div>
            
            <!-- Universal Components -->
            <div class="component-section" data-platform="all">
              <h3 class="section-title">🎨 Universal Components</h3>
              <div class="component-grid">
                <div class="component-item" draggable="true" data-component="hero-section">
                  <span class="component-icon">🌟</span>
                  <p class="component-name">Hero Section</p>
                  <span class="platform-badge universal">Universal</span>
                </div>
                <div class="component-item premium" draggable="true" data-component="product-showcase">
                  <span class="component-icon">🛍️</span>
                  <p class="component-name">Product Showcase</p>
                  <span class="platform-badge universal">Universal</span>
                </div>
                <div class="component-item" draggable="true" data-component="testimonial">
                  <span class="component-icon">💬</span>
                  <p class="component-name">Testimonial</p>
                  <span class="platform-badge universal">Universal</span>
                </div>
                <div class="component-item premium" draggable="true" data-component="pricing-table">
                  <span class="component-icon">💰</span>
                  <p class="component-name">Pricing Table</p>
                  <span class="platform-badge universal">Universal</span>
                </div>
                <div class="component-item" draggable="true" data-component="contact-form">
                  <span class="component-icon">📝</span>
                  <p class="component-name">Contact Form</p>
                  <span class="platform-badge universal">Universal</span>
                </div>
                <div class="component-item premium" draggable="true" data-component="countdown-timer">
                  <span class="component-icon">⏰</span>
                  <p class="component-name">Countdown</p>
                  <span class="platform-badge universal">Universal</span>
                </div>
              </div>
            </div>
            
            <div class="component-section">
              <h3 class="section-title">Interactive</h3>
              <div class="component-grid">
                <div class="component-item" draggable="true" data-component="form">
                  <span class="component-icon">📋</span>
                  <p class="component-name">Form</p>
                </div>
                <div class="component-item" draggable="true" data-component="countdown">
                  <span class="component-icon">⏰</span>
                  <p class="component-name">Countdown</p>
                </div>
                <div class="component-item" draggable="true" data-component="video">
                  <span class="component-icon">🎥</span>
                  <p class="component-name">Video</p>
                </div>
                <div class="component-item" draggable="true" data-component="gallery">
                  <span class="component-icon">🖼️</span>
                  <p class="component-name">Gallery</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- AI Studio Tab -->
        <div id="ai-tab" class="tab-pane">
          <div class="tab-content">
            <div class="ai-studio-header">
              <h3 class="studio-title">🤖 AI Campaign Generator</h3>
              <p class="studio-subtitle">Create professional campaigns in seconds</p>
            </div>
            
            <!-- Quick Start Templates -->
            <div class="quick-start-section">
              <h4 class="quick-start-title">⚡ Quick Start</h4>
              <div class="quick-start-grid">
                <button class="quick-start-btn" onclick="generateQuickCampaign('product-launch')">
                  <span class="quick-icon">🚀</span>
                  <span class="quick-label">Product Launch</span>
                </button>
                <button class="quick-start-btn" onclick="generateQuickCampaign('sale-event')">
                  <span class="quick-icon">🏷️</span>
                  <span class="quick-label">Sale Event</span>
                </button>
                <button class="quick-start-btn" onclick="generateQuickCampaign('newsletter')">
                  <span class="quick-icon">📰</span>
                  <span class="quick-label">Newsletter</span>
                </button>
                <button class="quick-start-btn" onclick="generateQuickCampaign('social-media')">
                  <span class="quick-icon">📱</span>
                  <span class="quick-label">Social Posts</span>
                </button>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Custom AI Generation -->
            <form id="aiGenerationForm">
              <div class="form-group">
                <label class="form-label">✨ Describe your campaign vision</label>
                <textarea 
                  class="form-textarea enhanced" 
                  rows="3" 
                  placeholder="E.g., 'Create a Black Friday email campaign with countdown timer and product showcase for a tech startup selling productivity apps. Make it modern and urgent.'"
                  id="aiPrompt"
                ></textarea>
                <div class="form-hint">💡 Pro tip: Be specific about your goals, target audience, and desired style</div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label class="form-label">🎯 Campaign Type</label>
                  <select class="form-select enhanced" id="campaignType">
                    <option value="email-marketing">📧 Email Marketing</option>
                    <option value="social-media">📱 Social Media</option>
                    <option value="landing-page">🌐 Landing Page</option>
                    <option value="ad-campaign">📺 Ad Campaign</option>
                    <option value="newsletter">📰 Newsletter</option>
                    <option value="tiktok-content">🎵 TikTok Content</option>
                    <option value="youtube-content">📺 YouTube Content</option>
                    <option value="instagram-story">📸 Instagram Story</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label class="form-label">🏢 Industry</label>
                  <select class="form-select enhanced" id="industry">
                    <option value="tech">💻 Technology</option>
                    <option value="ecommerce">🛒 E-commerce</option>
                    <option value="saas">☁️ SaaS</option>
                    <option value="finance">💰 Finance</option>
                    <option value="healthcare">🏥 Healthcare</option>
                    <option value="education">🎓 Education</option>
                    <option value="fashion">👗 Fashion</option>
                    <option value="food">🍕 Food & Beverage</option>
                    <option value="travel">✈️ Travel</option>
                    <option value="fitness">💪 Fitness</option>
                    <option value="real-estate">🏠 Real Estate</option>
                    <option value="nonprofit">❤️ Non-profit</option>
                  </select>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label class="form-label">🎭 Brand Tone</label>
                  <select class="form-select enhanced" id="tone">
                    <option value="professional">👔 Professional</option>
                    <option value="friendly">😊 Friendly</option>
                    <option value="trendy">🔥 Trendy</option>
                    <option value="luxury">💎 Luxury</option>
                    <option value="playful">🎉 Playful</option>
                    <option value="urgent">⚡ Urgent</option>
                    <option value="minimalist">✨ Minimalist</option>
                    <option value="bold">💥 Bold</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label class="form-label">🎨 Style</label>
                  <select class="form-select enhanced" id="style">
                    <option value="modern">🔮 Modern</option>
                    <option value="classic">🏛️ Classic</option>
                    <option value="creative">🎨 Creative</option>
                    <option value="corporate">🏢 Corporate</option>
                    <option value="startup">🚀 Startup</option>
                    <option value="artistic">🎭 Artistic</option>
                  </select>
                </div>
              </div>
              
              <div class="ai-options">
                <div class="option-group">
                  <input type="checkbox" id="includeImages" checked>
                  <label for="includeImages">🖼️ Include relevant images</label>
                </div>
                <div class="option-group">
                  <input type="checkbox" id="optimizeConversion" checked>
                  <label for="optimizeConversion">📈 Optimize for conversions</label>
                </div>
                <div class="option-group">
                  <input type="checkbox" id="mobileFriendly" checked>
                  <label for="mobileFriendly">📱 Mobile-first design</label>
                </div>
              </div>
              
              <button type="button" class="btn btn-ai-generate" onclick="generateWithAI()">
                <span class="btn-icon">✨</span>
                <span class="btn-text">Generate Campaign</span>
                <span class="btn-subtext">Powered by AI</span>
              </button>
            </form>
            
            <!-- AI Generation Status -->
            <div id="aiGenerationStatus" class="generation-status" style="display: none;">
              <div class="status-content">
                <div class="loading-animation">
                  <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
                <p class="status-text">AI is crafting your campaign...</p>
                <p class="status-subtext">This usually takes 10-15 seconds</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Professional Templates Tab -->
        <div id="templates-tab" class="tab-pane">
          <div class="tab-content">
            <div class="templates-header">
              <h3 class="studio-title">📚 Professional Templates</h3>
              <p class="studio-subtitle">Industry-tested templates that convert</p>
            </div>
            
            <div class="search-box">
              <input type="text" class="search-input" placeholder="Search templates..." id="templateSearch">
              <span class="search-icon">🔍</span>
            </div>
            
            <!-- Template Categories -->
            <div class="template-categories">
              <button class="category-btn active" data-category="all">All Templates</button>
              <button class="category-btn" data-category="email">📧 Email</button>
              <button class="category-btn" data-category="social">📱 Social</button>
              <button class="category-btn" data-category="landing">🌐 Landing</button>
            </div>
            
            <!-- Featured Templates -->
            <div class="template-section">
              <h4 class="template-section-title">⭐ Featured Templates</h4>
              <div class="template-grid">
                <div class="template-card premium" onclick="loadTemplate('saas-launch')">
                  <div class="template-preview">
                    <div class="preview-placeholder saas">SaaS Launch</div>
                    <div class="template-badge premium">PRO</div>
                  </div>
                  <div class="template-info">
                    <h5 class="template-name">SaaS Product Launch</h5>
                    <p class="template-desc">Complete campaign suite for software launches</p>
                    <div class="template-stats">
                      <span class="stat">📈 95% CTR</span>
                      <span class="stat">💰 High Convert</span>
                    </div>
                  </div>
                </div>
                
                <div class="template-card" onclick="loadTemplate('black-friday')">
                  <div class="template-preview">
                    <div class="preview-placeholder sale">Black Friday</div>
                  </div>
                  <div class="template-info">
                    <h5 class="template-name">Black Friday Sale</h5>
                    <p class="template-desc">High-impact sale campaign with urgency</p>
                    <div class="template-stats">
                      <span class="stat">🔥 Trending</span>
                      <span class="stat">⚡ Urgent</span>
                    </div>
                  </div>
                </div>
                
                <div class="template-card premium" onclick="loadTemplate('tiktok-viral')">
                  <div class="template-preview">
                    <div class="preview-placeholder tiktok">TikTok Viral</div>
                    <div class="template-badge premium">PRO</div>
                  </div>
                  <div class="template-info">
                    <h5 class="template-name">TikTok Viral Kit</h5>
                    <p class="template-desc">Viral TikTok content templates</p>
                    <div class="template-stats">
                      <span class="stat">🚀 Viral</span>
                      <span class="stat">🎵 TikTok</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Email Templates -->
            <div class="template-section" data-category="email">
              <h4 class="template-section-title">📧 Email Campaign Templates</h4>
              <div class="template-list">
                <div class="template-list-item" onclick="loadTemplate('welcome-series')">
                  <div class="template-icon">👋</div>
                  <div class="template-details">
                    <h5 class="template-title">Welcome Email Series</h5>
                    <p class="template-subtitle">5-part onboarding sequence</p>
                  </div>
                  <div class="template-metrics">
                    <span class="metric">🎯 68% Open Rate</span>
                  </div>
                </div>
                
                <div class="template-list-item premium" onclick="loadTemplate('abandoned-cart')">
                  <div class="template-icon">🛒</div>
                  <div class="template-details">
                    <h5 class="template-title">Abandoned Cart Recovery</h5>
                    <p class="template-subtitle">3-step cart recovery sequence</p>
                  </div>
                  <div class="template-metrics">
                    <span class="metric">💰 42% Recovery Rate</span>
                    <span class="badge premium">PRO</span>
                  </div>
                </div>
                
                <div class="template-list-item" onclick="loadTemplate('newsletter-modern')">
                  <div class="template-icon">📰</div>
                  <div class="template-details">
                    <h5 class="template-title">Modern Newsletter</h5>
                    <p class="template-subtitle">Clean, professional design</p>
                  </div>
                  <div class="template-metrics">
                    <span class="metric">📈 85% Engagement</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Social Media Templates -->
            <div class="template-section" data-category="social">
              <h4 class="template-section-title">📱 Social Media Templates</h4>
              <div class="social-grid">
                <div class="social-template" onclick="loadTemplate('instagram-story-suite')">
                  <div class="social-preview instagram">IG Stories</div>
                  <span class="social-label">📸 Instagram Stories</span>
                </div>
                <div class="social-template premium" onclick="loadTemplate('tiktok-trending')">
                  <div class="social-preview tiktok">TikTok</div>
                  <span class="social-label">🎵 TikTok Content</span>
                </div>
                <div class="social-template" onclick="loadTemplate('linkedin-posts')">
                  <div class="social-preview linkedin">LinkedIn</div>
                  <span class="social-label">💼 LinkedIn Posts</span>
                </div>
                <div class="social-template" onclick="loadTemplate('youtube-thumbnails')">
                  <div class="social-preview youtube">YouTube</div>
                  <span class="social-label">📺 YT Thumbnails</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Brand Kit Tab -->
        <div id="brand-tab" class="tab-pane">
          <div class="tab-content">
            <div class="brand-header">
              <h3 class="studio-title">🎨 Brand Kit</h3>
              <p class="studio-subtitle">Maintain consistent brand identity across all campaigns</p>
            </div>
            
            <!-- Brand Colors -->
            <div class="brand-section">
              <h4 class="brand-section-title">🎨 Brand Colors</h4>
              <div class="color-palette">
                <div class="color-item">
                  <div class="color-swatch" style="background: #3b82f6;"></div>
                  <span class="color-label">Primary</span>
                  <span class="color-code">#3b82f6</span>
                </div>
                <div class="color-item">
                  <div class="color-swatch" style="background: #1f2937;"></div>
                  <span class="color-label">Secondary</span>
                  <span class="color-code">#1f2937</span>
                </div>
                <div class="color-item">
                  <div class="color-swatch" style="background: #10b981;"></div>
                  <span class="color-label">Accent</span>
                  <span class="color-code">#10b981</span>
                </div>
                <button class="add-color-btn">+ Add Color</button>
              </div>
            </div>
            
            <!-- Brand Fonts -->
            <div class="brand-section">
              <h4 class="brand-section-title">📝 Typography</h4>
              <div class="font-stack">
                <div class="font-item">
                  <span class="font-preview" style="font-family: 'Inter', sans-serif; font-weight: 600;">Inter Bold</span>
                  <span class="font-usage">Headings</span>
                </div>
                <div class="font-item">
                  <span class="font-preview" style="font-family: 'Inter', sans-serif;">Inter Regular</span>
                  <span class="font-usage">Body text</span>
                </div>
                <button class="add-font-btn">+ Add Font</button>
              </div>
            </div>
            
            <!-- Brand Assets -->
            <div class="brand-section">
              <h4 class="brand-section-title">📁 Brand Assets</h4>
              <div class="asset-grid">
                <div class="asset-item">
                  <div class="asset-preview logo">
                    <span class="asset-icon">🎯</span>
                  </div>
                  <span class="asset-label">Logo</span>
                </div>
                <div class="asset-item">
                  <div class="asset-preview images">
                    <span class="asset-icon">🖼️</span>
                  </div>
                  <span class="asset-label">Images</span>
                </div>
                <div class="asset-item add-asset">
                  <div class="asset-preview">
                    <span class="asset-icon">+</span>
                  </div>
                  <span class="asset-label">Upload</span>
                </div>
              </div>
            </div>
            
            <!-- Brand Voice -->
            <div class="brand-section">
              <h4 class="brand-section-title">🗣️ Brand Voice</h4>
              <div class="voice-settings">
                <div class="voice-option">
                  <label class="voice-label">Tone</label>
                  <select class="voice-select">
                    <option value="professional">Professional</option>
                    <option value="friendly">Friendly</option>
                    <option value="casual">Casual</option>
                    <option value="authoritative">Authoritative</option>
                  </select>
                </div>
                <div class="voice-option">
                  <label class="voice-label">Personality</label>
                  <div class="personality-tags">
                    <span class="tag active">Helpful</span>
                    <span class="tag">Expert</span>
                    <span class="tag">Approachable</span>
                    <span class="tag">Innovative</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Brand Guidelines -->
            <div class="brand-section">
              <h4 class="brand-section-title">📋 Quick Guidelines</h4>
              <div class="guidelines-list">
                <div class="guideline-item">
                  <span class="guideline-icon">✅</span>
                  <span class="guideline-text">Always use primary brand colors for CTAs</span>
                </div>
                <div class="guideline-item">
                  <span class="guideline-icon">✅</span>
                  <span class="guideline-text">Maintain 1.5x line height for readability</span>
                </div>
                <div class="guideline-item">
                  <span class="guideline-icon">⚠️</span>
                  <span class="guideline-text">Keep subject lines under 50 characters</span>
                </div>
              </div>
            </div>
            
            <button class="btn btn-brand-apply" onclick="applyBrandKit()">
              <span class="btn-icon">🎨</span>
              <span class="btn-text">Apply Brand Kit to Campaign</span>
            </button>
          </div>
        </div>
      </div>
      
      <div class="version-info">
        🚀 RapidMarkt Studio v3.0 - Multi-Platform Campaign Builder
      </div>
    </aside>

    <!-- Main Canvas Area -->
    <main class="canvas-area" id="canvasArea">
      <div class="canvas-container" id="canvasContainer">
        <div class="floating-toolbar">
          <button class="toolbar-btn" onclick="undoAction()" title="Undo" id="undoBtn">
            ↶
          </button>
          <button class="toolbar-btn" onclick="redoAction()" title="Redo" id="redoBtn">
            ↷
          </button>
          <button class="toolbar-btn" onclick="zoomOut()" title="Zoom Out">
            🔍-
          </button>
          <button class="toolbar-btn" onclick="zoomIn()" title="Zoom In">
            🔍+
          </button>
          <button class="toolbar-btn" onclick="toggleGrid()" title="Toggle Grid">
            ⊞
          </button>
          <button class="toolbar-btn" onclick="clearCanvas()" title="Clear All">
            🗑️
          </button>
        </div>
        
        <div class="canvas-content" id="canvasContent">
          <div class="drop-zone" id="dropZone">
            <div class="drop-zone-icon">📄</div>
            <h3 class="drop-zone-title">Start Building Your Template</h3>
            <p class="drop-zone-subtitle">Drag components from the sidebar or use AI to generate content</p>
          </div>
        </div>
      </div>
    </main>

    <!-- Right Properties Panel -->
    <aside class="properties-panel" id="propertiesPanel">
      <div class="properties-header">
        <h2 class="properties-title">Properties</h2>
        <p style="margin: 0; font-size: 14px; color: var(--gray-600);">Customize selected element</p>
      </div>
      
      <div class="properties-content" id="propertiesContent">
        <div style="text-align: center; padding: 40px 20px; color: var(--gray-500);">
          <div style="font-size: 32px; margin-bottom: 16px;">⚙️</div>
          <p style="margin: 0;">Select an element to edit its properties</p>
        </div>
      </div>
    </aside>
  </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
  </div>

  <!-- Templates Form (Hidden) -->
  <%= form_with model: (@template || Template.new), url: (@template&.persisted? ? template_path(@template) : templates_path), method: (@template&.persisted? ? :patch : :post), local: true, id: "templateForm", style: "display: none;" do |form| %>
    <%= form.hidden_field :name, id: "templateFormName" %>
    <%= form.hidden_field :subject, id: "templateFormSubject" %>
    <%= form.hidden_field :body, id: "templateFormBody" %>
    <%= form.hidden_field :template_type, id: "templateFormType", value: "email" %>
    <%= form.hidden_field :status, id: "templateFormStatus", value: "draft" %>
    <%= form.hidden_field :design_config, id: "templateFormDesignConfig" %>
    <%= form.hidden_field :variables, id: "templateFormVariables" %>
    <%= form.hidden_field :content_blocks, id: "templateFormContentBlocks" %>
  <% end %>

  <script>
    // Template Builder State
    class TemplateBuilder {
      constructor() {
        this.elements = [];
        this.selectedElement = null;
        this.history = [];
        this.historyIndex = -1;
        this.draggedComponent = null;
        this.zoom = 1;
        this.currentDevice = 'desktop';
        this.autoSaveInterval = null;
        
        this.init();
      }
      
      init() {
        console.log('Initializing Template Builder...');
        
        // Initialize core properties
        this.elements = [];
        this.selectedElement = null;
        this.currentDevice = 'desktop';
        this.zoom = 1;
        this.history = [];
        this.historyIndex = -1;
        this.draggedComponent = null;
        this.dropTargets = [];
        
        // Setup event listeners first
        this.setupEventListeners();
        this.setupTabEventListeners();
        
        // Setup drag and drop with retry mechanism
        this.setupDragAndDropWithRetry();
        
        // Setup auto-save
        this.setupAutoSave();
        
        // Load any existing draft
        this.loadDraftIfExists();
        
        // Initialize first tab
        setTimeout(() => {
          this.switchTab('components');
        }, 100);
        
        // Initialize platform filters
        this.initializePlatformFilters();
        
        console.log('Template Builder initialized successfully');
      }
      
      setupTabEventListeners() {
        console.log('Setting up tab event listeners...');
        
        // Remove onclick attributes and use proper event listeners
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(button => {
          // Extract tab name from onclick attribute or data attribute
          const onclickAttr = button.getAttribute('onclick');
          let tabName = '';
          
          if (onclickAttr) {
            const match = onclickAttr.match(/switchTab\('([^']+)'\)/);
            if (match) {
              tabName = match[1];
              button.removeAttribute('onclick'); // Remove onclick
              button.dataset.tab = tabName; // Store in data attribute
            }
          }
          
          // Add event listener
          button.addEventListener('click', (e) => {
            e.preventDefault();
            const tab = e.currentTarget.dataset.tab;
            if (tab) {
              console.log('Tab button clicked:', tab);
              this.switchTab(tab);
            }
          });
          
          console.log('Added event listener to tab button:', tabName);
        });
      }
      
      setupDragAndDropWithRetry() {
        const maxRetries = 3;
        let retryCount = 0;
        
        const trySetup = () => {
          try {
            this.setupDragAndDrop();
          } catch (error) {
            console.error('Drag and drop setup failed:', error);
            retryCount++;
            if (retryCount < maxRetries) {
              console.log(`Retrying drag and drop setup (${retryCount}/${maxRetries})...`);
              setTimeout(trySetup, 500);
            }
          }
        };
        
        trySetup();
      }
      
      initializePlatformFilters() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
          if (!btn.hasAttribute('data-filter-initialized')) {
            btn.addEventListener('click', (e) => {
              this.handlePlatformFilter(e.target.dataset.platform);
            });
            btn.setAttribute('data-filter-initialized', 'true');
          }
        });
      }
      
      handlePlatformFilter(platform) {
        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        event.target.classList.add('active');
        
        // Filter component sections
        const sections = document.querySelectorAll('.component-section');
        sections.forEach(section => {
          const sectionPlatform = section.dataset.platform;
          const isVisible = platform === 'all' || sectionPlatform === platform || sectionPlatform === 'all';
          section.style.display = isVisible ? 'block' : 'none';
        });
      }
      
      setupEventListeners() {
        // Template name input
        document.getElementById('templateName').addEventListener('input', (e) => {
          this.saveState();
        });
        
        // Device preview buttons
        document.querySelectorAll('.device-btn').forEach(btn => {
          btn.addEventListener('click', (e) => {
            document.querySelectorAll('.device-btn').forEach(b => b.classList.remove('active'));
            e.target.classList.add('active');
            this.currentDevice = e.target.dataset.device;
            this.updateCanvasSize();
          });
        });
        
        // Search functionality
        document.getElementById('componentSearch').addEventListener('input', (e) => {
          this.filterComponents(e.target.value);
        });
        
        // Canvas click handler
        document.getElementById('canvasContent').addEventListener('click', (e) => {
          if (e.target.closest('.template-element')) {
            this.selectElement(e.target.closest('.template-element'));
          } else {
            this.deselectElement();
          }
        });
      }
      
      setupDragAndDrop() {
        console.log('Setting up drag and drop...');
        
        try {
          // Clear any existing drag state
          this.draggedComponent = null;
          
          // Remove existing event listeners to prevent duplicates
          if (this.dragStartHandler) {
            document.removeEventListener('dragstart', this.dragStartHandler);
            document.removeEventListener('dragend', this.dragEndHandler);
          }
          
          // Create bound handlers that we can remove later
          this.dragStartHandler = this.handleDragStart.bind(this);
          this.dragEndHandler = this.handleDragEnd.bind(this);
          
          // Setup draggable components with event delegation
          document.addEventListener('dragstart', this.dragStartHandler);
          document.addEventListener('dragend', this.dragEndHandler);
          
          // Find all drop targets
          const canvasContainer = document.getElementById('canvasContainer');
          const canvasContent = document.getElementById('canvasContent');
          const dropZone = document.getElementById('dropZone');
          
          console.log('Drop targets found:', {
            canvasContainer: !!canvasContainer,
            canvasContent: !!canvasContent,
            dropZone: !!dropZone
          });
          
          // Remove existing drop listeners
          if (this.dropTargets) {
            this.dropTargets.forEach(target => {
              if (target.element && target.handlers) {
                target.element.removeEventListener('dragover', target.handlers.dragover);
                target.element.removeEventListener('dragleave', target.handlers.dragleave);
                target.element.removeEventListener('drop', target.handlers.drop);
              }
            });
          }
          
          // Setup new drop targets
          this.dropTargets = [];
          const targets = [canvasContainer, canvasContent, dropZone].filter(Boolean);
          
          targets.forEach(element => {
            const handlers = {
              dragover: this.handleDragOver.bind(this),
              dragleave: this.handleDragLeave.bind(this),
              drop: this.handleDrop.bind(this)
            };
            
            element.addEventListener('dragover', handlers.dragover);
            element.addEventListener('dragleave', handlers.dragleave);
            element.addEventListener('drop', handlers.drop);
            
            this.dropTargets.push({ element, handlers });
            console.log('Added drop listeners to:', element.id || element.className);
          });
          
          // Ensure all component items are draggable
          this.updateDraggableComponents();
          
          console.log('Drag and drop setup complete with', this.dropTargets.length, 'drop targets');
          
        } catch (error) {
          console.error('Error setting up drag and drop:', error);
        }
      }
      
      updateDraggableComponents() {
        const componentItems = document.querySelectorAll('.component-item');
        console.log('Found component items:', componentItems.length);
        
        componentItems.forEach(item => {
          if (!item.getAttribute('draggable')) {
            item.setAttribute('draggable', 'true');
            console.log('Made draggable:', item.dataset.component);
          }
        });
      }
      
      handleDragStart(e) {
        const componentItem = e.target.closest('.component-item');
        if (componentItem && componentItem.dataset.component) {
          console.log('Drag started:', componentItem.dataset.component);
          this.draggedComponent = componentItem.dataset.component;
          e.dataTransfer.effectAllowed = 'copy';
          e.dataTransfer.setData('text/plain', this.draggedComponent);
          
          // Visual feedback
          componentItem.style.opacity = '0.5';
          componentItem.style.transform = 'scale(0.95)';
          
          // Add dragging class to body
          document.body.classList.add('dragging');
        }
      }
      
      handleDragEnd(e) {
        const componentItem = e.target.closest('.component-item');
        if (componentItem) {
          componentItem.style.opacity = '1';
          componentItem.style.transform = 'scale(1)';
        }
        
        // Remove dragging class
        document.body.classList.remove('dragging');
        
        // Clean up any drag-over states
        document.querySelectorAll('.drag-over').forEach(el => {
          el.classList.remove('drag-over');
        });
      }
      
      handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.dataTransfer.dropEffect = 'copy';
        
        if (!this.draggedComponent) return;
        
        const element = e.currentTarget;
        if (!element.classList.contains('drag-over')) {
          element.classList.add('drag-over');
          console.log('Drag over:', element.id || element.className);
        }
      }
      
      handleDragLeave(e) {
        const element = e.currentTarget;
        // Only remove drag-over if we're really leaving the element
        if (!element.contains(e.relatedTarget)) {
          element.classList.remove('drag-over');
          console.log('Drag leave:', element.id || element.className);
        }
      }
      
      handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const element = e.currentTarget;
        element.classList.remove('drag-over');
        console.log('Drop event triggered on:', element.id || element.className, 'component:', this.draggedComponent);
        
        if (this.draggedComponent) {
          try {
            this.addComponent(this.draggedComponent);
            this.draggedComponent = null;
            this.showNotification('Component added successfully!', 'success');
          } catch (error) {
            console.error('Error adding component:', error);
            this.showNotification('Error adding component', 'error');
          }
        }
      }
      
      setupAutoSave() {
        this.autoSaveInterval = setInterval(() => {
          this.autoSave();
        }, 30000); // Auto-save every 30 seconds
      }
      
      addComponent(componentType) {
        const component = this.createComponent(componentType);
        const element = this.renderComponent(component);
        
        // Hide drop zone if this is the first component
        if (this.elements.length === 0) {
          document.getElementById('dropZone').style.display = 'none';
        }
        
        this.elements.push(component);
        document.getElementById('canvasContent').appendChild(element);
        
        this.selectElement(element);
        this.saveState();
        this.showNotification('Component added successfully!', 'success');
      }
      
      createComponent(type) {
        const id = 'element_' + Date.now();
        const baseComponent = {
          id: id,
          type: type,
          order: this.elements.length
        };
        
        switch (type) {
          case 'header':
            return {
              ...baseComponent,
              content: {
                title: 'Your Company Name',
                tagline: 'Building the future together',
                backgroundColor: '#ffffff',
                textColor: '#333333',
                logoUrl: '',
                navigation: ['Home', 'About', 'Services', 'Contact']
              }
            };
            
          case 'text-block':
            return {
              ...baseComponent,
              content: {
                text: 'Add your content here. You can use {{first_name}} and other variables.',
                fontSize: '16',
                textColor: '#333333',
                textAlign: 'left',
                fontWeight: 'normal'
              }
            };
            
          case 'image':
            return {
              ...baseComponent,
              content: {
                src: 'https://via.placeholder.com/600x300',
                alt: 'Image description',
                width: '100%',
                height: 'auto',
                alignment: 'center'
              }
            };
            
          case 'button':
            return {
              ...baseComponent,
              content: {
                text: 'Call to Action',
                url: '#',
                backgroundColor: '#3b82f6',
                textColor: '#ffffff',
                padding: '12px 24px',
                borderRadius: '8px',
                alignment: 'center'
              }
            };
            
          case 'hero-section':
            return {
              ...baseComponent,
              content: {
                headline: 'Transform Your Business Today',
                subheadline: 'Discover powerful solutions that drive growth and success',
                ctaText: 'Get Started Now',
                ctaUrl: '#',
                backgroundImage: 'https://via.placeholder.com/1200x600',
                overlayOpacity: '0.5',
                textColor: '#ffffff'
              }
            };
            
          case 'product-showcase':
            return {
              ...baseComponent,
              content: {
                title: 'Featured Product',
                description: 'This amazing product will change your life',
                price: '$99.99',
                originalPrice: '$129.99',
                imageUrl: 'https://via.placeholder.com/400x400',
                ctaText: 'Shop Now',
                ctaUrl: '#'
              }
            };
            
          case 'testimonial':
            return {
              ...baseComponent,
              content: {
                quote: 'This service exceeded all my expectations. Highly recommended!',
                author: 'Sarah Johnson',
                title: 'CEO, TechStart',
                avatar: 'https://via.placeholder.com/80x80',
                rating: 5
              }
            };
            
          case 'form':
            return {
              ...baseComponent,
              content: {
                title: 'Get In Touch',
                fields: ['name', 'email', 'message'],
                submitText: 'Send Message',
                backgroundColor: '#f9fafb'
              }
            };
            
          default:
            return baseComponent;
        }
      }
      
      renderComponent(component) {
        const element = document.createElement('div');
        element.className = 'template-element';
        element.dataset.elementId = component.id;
        element.dataset.elementType = component.type;
        
        // Add element controls
        const controls = document.createElement('div');
        controls.className = 'element-controls';
        controls.innerHTML = `
          <button class="control-btn" onclick="templateBuilder.moveElementUp('${component.id}')" title="Move Up">↑</button>
          <button class="control-btn" onclick="templateBuilder.moveElementDown('${component.id}')" title="Move Down">↓</button>
          <button class="control-btn" onclick="templateBuilder.duplicateElement('${component.id}')" title="Duplicate">⧉</button>
          <button class="control-btn" onclick="templateBuilder.removeElement('${component.id}')" title="Delete" style="background: #ef4444;">×</button>
        `;
        element.appendChild(controls);
        
        // Add component content
        const content = document.createElement('div');
        content.innerHTML = this.generateComponentHTML(component);
        element.appendChild(content);
        
        return element;
      }
      
      generateComponentHTML(component) {
        switch (component.type) {
          case 'header':
            return `
              <div style="background: ${component.content.backgroundColor}; color: ${component.content.textColor}; padding: 20px; text-align: center; border-bottom: 1px solid #e5e7eb;">
                <h1 style="margin: 0 0 8px 0; font-size: 28px; font-weight: bold;">${component.content.title}</h1>
                <p style="margin: 0; opacity: 0.8;">${component.content.tagline}</p>
                <nav style="margin-top: 16px;">
                  ${component.content.navigation.map(item => `<a href="#" style="color: inherit; text-decoration: none; margin: 0 16px;">${item}</a>`).join('')}
                </nav>
              </div>
            `;
            
          case 'text-block':
            return `
              <div style="color: ${component.content.textColor}; font-size: ${component.content.fontSize}px; text-align: ${component.content.textAlign}; font-weight: ${component.content.fontWeight}; line-height: 1.6; margin: 16px 0;">
                ${component.content.text}
              </div>
            `;
            
          case 'image':
            return `
              <div style="text-align: ${component.content.alignment}; margin: 16px 0;">
                <img src="${component.content.src}" alt="${component.content.alt}" style="width: ${component.content.width}; height: ${component.content.height}; border-radius: 8px;">
              </div>
            `;
            
          case 'button':
            return `
              <div style="text-align: ${component.content.alignment}; margin: 24px 0;">
                <a href="${component.content.url}" style="
                  display: inline-block;
                  background: ${component.content.backgroundColor};
                  color: ${component.content.textColor};
                  padding: ${component.content.padding};
                  border-radius: ${component.content.borderRadius};
                  text-decoration: none;
                  font-weight: 600;
                  transition: all 0.2s;
                ">${component.content.text}</a>
              </div>
            `;
            
          case 'hero-section':
            return `
              <div style="
                background: linear-gradient(rgba(0,0,0,${component.content.overlayOpacity}), rgba(0,0,0,${component.content.overlayOpacity})), url('${component.content.backgroundImage}');
                background-size: cover;
                background-position: center;
                color: ${component.content.textColor};
                padding: 80px 20px;
                text-align: center;
                border-radius: 12px;
                margin: 16px 0;
              ">
                <h1 style="margin: 0 0 16px 0; font-size: 48px; font-weight: bold;">${component.content.headline}</h1>
                <p style="margin: 0 0 32px 0; font-size: 20px; opacity: 0.9;">${component.content.subheadline}</p>
                <a href="${component.content.ctaUrl}" style="
                  display: inline-block;
                  background: #3b82f6;
                  color: white;
                  padding: 16px 32px;
                  border-radius: 8px;
                  text-decoration: none;
                  font-weight: 600;
                  font-size: 18px;
                ">${component.content.ctaText}</a>
              </div>
            `;
            
          case 'product-showcase':
            return `
              <div style="border: 1px solid #e5e7eb; border-radius: 12px; padding: 24px; margin: 16px 0; text-align: center;">
                <img src="${component.content.imageUrl}" alt="${component.content.title}" style="width: 200px; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 16px;">
                <h3 style="margin: 0 0 8px 0; font-size: 24px; font-weight: bold;">${component.content.title}</h3>
                <p style="margin: 0 0 16px 0; color: #6b7280;">${component.content.description}</p>
                <div style="margin-bottom: 20px;">
                  <span style="font-size: 28px; font-weight: bold; color: #ef4444;">${component.content.price}</span>
                  <span style="font-size: 18px; text-decoration: line-through; color: #9ca3af; margin-left: 8px;">${component.content.originalPrice}</span>
                </div>
                <a href="${component.content.ctaUrl}" style="
                  display: inline-block;
                  background: #10b981;
                  color: white;
                  padding: 12px 24px;
                  border-radius: 8px;
                  text-decoration: none;
                  font-weight: 600;
                ">${component.content.ctaText}</a>
              </div>
            `;
            
          case 'testimonial':
            return `
              <div style="background: #f9fafb; border-left: 4px solid #3b82f6; padding: 24px; margin: 16px 0; border-radius: 8px;">
                <div style="display: flex; align-items: center; margin-bottom: 16px;">
                  <img src="${component.content.avatar}" alt="${component.content.author}" style="width: 60px; height: 60px; border-radius: 50%; margin-right: 16px;">
                  <div>
                    <h4 style="margin: 0 0 4px 0; font-weight: 600;">${component.content.author}</h4>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">${component.content.title}</p>
                    <div style="color: #fbbf24; margin-top: 4px;">${'★'.repeat(component.content.rating)}${'☆'.repeat(5 - component.content.rating)}</div>
                  </div>
                </div>
                <blockquote style="margin: 0; font-style: italic; font-size: 16px; line-height: 1.6;">"${component.content.quote}"</blockquote>
              </div>
            `;
            
          case 'form':
            return `
              <div style="background: ${component.content.backgroundColor}; padding: 32px; border-radius: 12px; margin: 16px 0;">
                <h3 style="margin: 0 0 24px 0; text-align: center; font-size: 24px; font-weight: bold;">${component.content.title}</h3>
                <form style="max-width: 400px; margin: 0 auto;">
                  ${component.content.fields.map(field => {
                    if (field === 'message') {
                      return `<textarea placeholder="Your message..." style="width: 100%; padding: 12px; margin-bottom: 16px; border: 1px solid #d1d5db; border-radius: 8px; font-family: inherit; resize: vertical; min-height: 100px;"></textarea>`;
                    } else {
                      return `<input type="${field === 'email' ? 'email' : 'text'}" placeholder="${field === 'name' ? 'Your Name' : field === 'email' ? '<EMAIL>' : field}" style="width: 100%; padding: 12px; margin-bottom: 16px; border: 1px solid #d1d5db; border-radius: 8px; font-family: inherit;">`;
                    }
                  }).join('')}
                  <button type="submit" style="width: 100%; background: #3b82f6; color: white; border: none; padding: 12px; border-radius: 8px; font-weight: 600; cursor: pointer;">${component.content.submitText}</button>
                </form>
              </div>
            `;
            
          default:
            return `<div style="padding: 20px; text-align: center; border: 2px dashed #d1d5db;">Unknown component type: ${component.type}</div>`;
        }
      }
      
      selectElement(element) {
        // Remove previous selection
        document.querySelectorAll('.template-element.selected').forEach(el => {
          el.classList.remove('selected');
        });
        
        // Select new element
        element.classList.add('selected');
        this.selectedElement = element;
        
        // Update properties panel
        this.updatePropertiesPanel(element);
      }
      
      deselectElement() {
        document.querySelectorAll('.template-element.selected').forEach(el => {
          el.classList.remove('selected');
        });
        this.selectedElement = null;
        this.clearPropertiesPanel();
      }
      
      updatePropertiesPanel(element) {
        const elementId = element.dataset.elementId;
        const component = this.elements.find(el => el.id === elementId);
        
        if (!component) return;
        
        const panel = document.getElementById('propertiesContent');
        panel.innerHTML = this.generatePropertiesHTML(component);
        
        // Add event listeners to property inputs
        panel.querySelectorAll('input, select, textarea').forEach(input => {
          input.addEventListener('input', () => {
            this.updateElementProperty(component, input);
          });
        });
      }
      
      generatePropertiesHTML(component) {
        let html = `<h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; text-transform: capitalize;">${component.type.replace('-', ' ')} Properties</h3>`;
        
        switch (component.type) {
          case 'header':
            html += `
              <div class="form-group">
                <label class="form-label">Company Name</label>
                <input type="text" class="form-input" data-property="title" value="${component.content.title}">
              </div>
              <div class="form-group">
                <label class="form-label">Tagline</label>
                <input type="text" class="form-input" data-property="tagline" value="${component.content.tagline}">
              </div>
              <div class="form-group">
                <label class="form-label">Background Color</label>
                <div class="color-picker">
                  <input type="color" class="color-input" data-property="backgroundColor" value="${component.content.backgroundColor}">
                  <input type="text" class="form-input" data-property="backgroundColor" value="${component.content.backgroundColor}" style="flex: 1;">
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">Text Color</label>
                <div class="color-picker">
                  <input type="color" class="color-input" data-property="textColor" value="${component.content.textColor}">
                  <input type="text" class="form-input" data-property="textColor" value="${component.content.textColor}" style="flex: 1;">
                </div>
              </div>
            `;
            break;
            
          case 'text-block':
            html += `
              <div class="form-group">
                <label class="form-label">Content</label>
                <textarea class="form-textarea" data-property="text" rows="4">${component.content.text}</textarea>
              </div>
              <div class="form-group">
                <label class="form-label">Font Size</label>
                <input type="number" class="form-input" data-property="fontSize" value="${component.content.fontSize}" min="12" max="48">
              </div>
              <div class="form-group">
                <label class="form-label">Text Color</label>
                <div class="color-picker">
                  <input type="color" class="color-input" data-property="textColor" value="${component.content.textColor}">
                  <input type="text" class="form-input" data-property="textColor" value="${component.content.textColor}" style="flex: 1;">
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">Alignment</label>
                <select class="form-select" data-property="textAlign">
                  <option value="left" ${component.content.textAlign === 'left' ? 'selected' : ''}>Left</option>
                  <option value="center" ${component.content.textAlign === 'center' ? 'selected' : ''}>Center</option>
                  <option value="right" ${component.content.textAlign === 'right' ? 'selected' : ''}>Right</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">Font Weight</label>
                <select class="form-select" data-property="fontWeight">
                  <option value="normal" ${component.content.fontWeight === 'normal' ? 'selected' : ''}>Normal</option>
                  <option value="bold" ${component.content.fontWeight === 'bold' ? 'selected' : ''}>Bold</option>
                </select>
              </div>
            `;
            break;
            
          case 'button':
            html += `
              <div class="form-group">
                <label class="form-label">Button Text</label>
                <input type="text" class="form-input" data-property="text" value="${component.content.text}">
              </div>
              <div class="form-group">
                <label class="form-label">Link URL</label>
                <input type="url" class="form-input" data-property="url" value="${component.content.url}">
              </div>
              <div class="form-group">
                <label class="form-label">Background Color</label>
                <div class="color-picker">
                  <input type="color" class="color-input" data-property="backgroundColor" value="${component.content.backgroundColor}">
                  <input type="text" class="form-input" data-property="backgroundColor" value="${component.content.backgroundColor}" style="flex: 1;">
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">Text Color</label>
                <div class="color-picker">
                  <input type="color" class="color-input" data-property="textColor" value="${component.content.textColor}">
                  <input type="text" class="form-input" data-property="textColor" value="${component.content.textColor}" style="flex: 1;">
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">Alignment</label>
                <select class="form-select" data-property="alignment">
                  <option value="left" ${component.content.alignment === 'left' ? 'selected' : ''}>Left</option>
                  <option value="center" ${component.content.alignment === 'center' ? 'selected' : ''}>Center</option>
                  <option value="right" ${component.content.alignment === 'right' ? 'selected' : ''}>Right</option>
                </select>
              </div>
            `;
            break;
            
          case 'image':
            html += `
              <div class="form-group">
                <label class="form-label">Image URL</label>
                <input type="url" class="form-input" data-property="src" value="${component.content.src}">
              </div>
              <div class="form-group">
                <label class="form-label">Alt Text</label>
                <input type="text" class="form-input" data-property="alt" value="${component.content.alt}">
              </div>
              <div class="form-group">
                <label class="form-label">Width</label>
                <input type="text" class="form-input" data-property="width" value="${component.content.width}">
              </div>
              <div class="form-group">
                <label class="form-label">Alignment</label>
                <select class="form-select" data-property="alignment">
                  <option value="left" ${component.content.alignment === 'left' ? 'selected' : ''}>Left</option>
                  <option value="center" ${component.content.alignment === 'center' ? 'selected' : ''}>Center</option>
                  <option value="right" ${component.content.alignment === 'right' ? 'selected' : ''}>Right</option>
                </select>
              </div>
            `;
            break;
        }
        
        return html;
      }
      
      updateElementProperty(component, input) {
        const property = input.dataset.property;
        const value = input.value;
        
        component.content[property] = value;
        
        // Update the visual element
        const element = document.querySelector(`[data-element-id="${component.id}"]`);
        const content = element.querySelector('div:last-child');
        content.innerHTML = this.generateComponentHTML(component);
        
        this.saveState();
      }
      
      clearPropertiesPanel() {
        document.getElementById('propertiesContent').innerHTML = `
          <div style="text-align: center; padding: 40px 20px; color: var(--gray-500);">
            <div style="font-size: 32px; margin-bottom: 16px;">⚙️</div>
            <p style="margin: 0;">Select an element to edit its properties</p>
          </div>
        `;
      }
      
      moveElementUp(elementId) {
        const index = this.elements.findIndex(el => el.id === elementId);
        if (index > 0) {
          [this.elements[index], this.elements[index - 1]] = [this.elements[index - 1], this.elements[index]];
          this.rerenderCanvas();
          this.saveState();
        }
      }
      
      moveElementDown(elementId) {
        const index = this.elements.findIndex(el => el.id === elementId);
        if (index < this.elements.length - 1) {
          [this.elements[index], this.elements[index + 1]] = [this.elements[index + 1], this.elements[index]];
          this.rerenderCanvas();
          this.saveState();
        }
      }
      
      duplicateElement(elementId) {
        const element = this.elements.find(el => el.id === elementId);
        if (element) {
          const duplicate = JSON.parse(JSON.stringify(element));
          duplicate.id = 'element_' + Date.now();
          
          const index = this.elements.findIndex(el => el.id === elementId);
          this.elements.splice(index + 1, 0, duplicate);
          
          this.rerenderCanvas();
          this.saveState();
          this.showNotification('Element duplicated!', 'success');
        }
      }
      
      removeElement(elementId) {
        this.elements = this.elements.filter(el => el.id !== elementId);
        
        // Show drop zone if no elements left
        if (this.elements.length === 0) {
          document.getElementById('dropZone').style.display = 'block';
        }
        
        this.rerenderCanvas();
        this.deselectElement();
        this.saveState();
        this.showNotification('Element removed!', 'success');
      }
      
      rerenderCanvas() {
        const canvasContent = document.getElementById('canvasContent');
        const dropZone = document.getElementById('dropZone');
        
        // Clear existing elements except drop zone
        Array.from(canvasContent.children).forEach(child => {
          if (child !== dropZone) {
            child.remove();
          }
        });
        
        // Re-render all elements
        this.elements.forEach(component => {
          const element = this.renderComponent(component);
          canvasContent.appendChild(element);
        });
      }
      
      saveState() {
        const state = {
          elements: JSON.parse(JSON.stringify(this.elements)),
          templateName: document.getElementById('templateName').value,
          timestamp: Date.now()
        };
        
        this.history = this.history.slice(0, this.historyIndex + 1);
        this.history.push(state);
        this.historyIndex++;
        
        // Limit history size
        if (this.history.length > 50) {
          this.history.shift();
          this.historyIndex--;
        }
        
        this.updateUndoRedoButtons();
      }
      
      undo() {
        if (this.historyIndex > 0) {
          this.historyIndex--;
          this.loadState(this.history[this.historyIndex]);
          this.updateUndoRedoButtons();
        }
      }
      
      redo() {
        if (this.historyIndex < this.history.length - 1) {
          this.historyIndex++;
          this.loadState(this.history[this.historyIndex]);
          this.updateUndoRedoButtons();
        }
      }
      
      loadState(state) {
        this.elements = JSON.parse(JSON.stringify(state.elements));
        document.getElementById('templateName').value = state.templateName;
        
        // Show/hide drop zone
        document.getElementById('dropZone').style.display = this.elements.length === 0 ? 'block' : 'none';
        
        this.rerenderCanvas();
        this.deselectElement();
      }
      
      updateUndoRedoButtons() {
        document.getElementById('undoBtn').disabled = this.historyIndex <= 0;
        document.getElementById('redoBtn').disabled = this.historyIndex >= this.history.length - 1;
      }
      
      clearCanvas() {
        if (confirm('Are you sure you want to clear all content?')) {
          this.elements = [];
          document.getElementById('dropZone').style.display = 'block';
          this.rerenderCanvas();
          this.deselectElement();
          this.saveState();
          this.showNotification('Canvas cleared!', 'success');
        }
      }
      
      updateCanvasSize() {
        const canvas = document.getElementById('canvasContainer');
        if (this.currentDevice === 'mobile') {
          canvas.style.maxWidth = '375px';
        } else {
          canvas.style.maxWidth = '600px';
        }
      }
      
      filterComponents(query) {
        const components = document.querySelectorAll('.component-item');
        components.forEach(component => {
          const name = component.querySelector('.component-name').textContent.toLowerCase();
          const visible = name.includes(query.toLowerCase());
          component.style.display = visible ? 'block' : 'none';
        });
      }
      
      switchTab(tabName) {
        console.log('Switching to tab:', tabName);
        
        try {
          // Hide all tab panes
          const allPanes = document.querySelectorAll('.tab-pane');
          console.log('Found tab panes:', allPanes.length);
          allPanes.forEach(pane => {
            pane.classList.remove('active');
            pane.style.display = 'none';
          });
          
          // Remove active class from all tab buttons
          const allBtns = document.querySelectorAll('.tab-btn');
          console.log('Found tab buttons:', allBtns.length);
          allBtns.forEach(btn => {
            btn.classList.remove('active');
          });
          
          // Show the selected tab pane
          const targetPane = document.getElementById(`${tabName}-tab`);
          if (targetPane) {
            targetPane.classList.add('active');
            targetPane.style.display = 'block';
            console.log('Activated tab pane:', targetPane.id);
          } else {
            console.error('Tab pane not found:', `${tabName}-tab`);
            // List all available tab panes for debugging
            const availablePanes = document.querySelectorAll('[id$="-tab"]');
            console.log('Available tab panes:', Array.from(availablePanes).map(p => p.id));
          }
          
          // Add active class to the clicked tab button using data attribute
          let targetBtn = document.querySelector(`[data-tab="${tabName}"]`);
          if (!targetBtn) {
            // Fallback: try onclick attribute (for backward compatibility)
            targetBtn = document.querySelector(`[onclick*="switchTab('${tabName}')"]`);
          }
          if (!targetBtn) {
            // Another fallback: find by text content
            const buttons = document.querySelectorAll('.tab-btn');
            targetBtn = Array.from(buttons).find(btn => 
              btn.textContent.toLowerCase().includes(tabName.toLowerCase())
            );
          }
          
          if (targetBtn) {
            targetBtn.classList.add('active');
            console.log('Activated tab button for:', tabName);
          } else {
            console.error('Tab button not found for:', tabName);
            const availableButtons = document.querySelectorAll('.tab-btn');
            console.log('Available tab buttons:', Array.from(availableButtons).map(b => 
              b.dataset.tab || b.getAttribute('onclick') || b.textContent.trim()
            ));
          }
          
          // Re-setup drag and drop for new content with a delay
          setTimeout(() => {
            try {
              this.setupDragAndDrop();
              console.log('Drag and drop re-initialized for tab:', tabName);
            } catch (e) {
              console.error('Error re-initializing drag and drop:', e);
            }
          }, 200);
          
          // Trigger specific actions based on tab
          this.handleTabSwitch(tabName);
          
        } catch (error) {
          console.error('Error in switchTab:', error);
        }
      }
      
      handleTabSwitch(tabName) {
        switch(tabName) {
          case 'components':
            // Ensure component search is properly initialized
            this.initializeComponentSearch();
            break;
          case 'ai':
            // Initialize AI form if needed
            this.initializeAIForm();
            break;
          case 'templates':
            // Load templates if needed
            this.loadTemplates();
            break;
          case 'brand':
            // Initialize brand tools
            this.initializeBrandTools();
            break;
        }
      }
      
      initializeComponentSearch() {
        const searchInput = document.getElementById('componentSearch');
        if (searchInput && !searchInput.hasAttribute('data-search-initialized')) {
          searchInput.addEventListener('input', (e) => {
            this.filterComponents(e.target.value);
          });
          searchInput.setAttribute('data-search-initialized', 'true');
        }
      }
      
      initializeAIForm() {
        // AI form initialization logic can go here
        console.log('AI form initialized');
      }
      
      loadTemplates() {
        // Template loading logic can go here
        console.log('Templates loaded');
      }
      
      initializeBrandTools() {
        // Brand tools initialization logic can go here
        console.log('Brand tools initialized');
      }
      
      filterComponents(searchTerm) {
        const components = document.querySelectorAll('.component-item');
        const lowercaseSearch = searchTerm.toLowerCase();
        
        components.forEach(component => {
          const name = component.querySelector('.component-name')?.textContent?.toLowerCase() || '';
          const isVisible = name.includes(lowercaseSearch);
          component.style.display = isVisible ? 'flex' : 'none';
        });
      }
      
      generateTemplateHTML() {
        let html = `
          <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        `;
        
        this.elements.forEach(component => {
          html += this.generateComponentHTML(component);
        });
        
        html += `
            <div style="margin-top: 40px; padding: 20px; text-align: center; color: #6b7280; font-size: 12px; border-top: 1px solid #e5e7eb;">
              <p style="margin: 0;">© ${new Date().getFullYear()} {{account.name}}. All rights reserved.</p>
              <p style="margin: 8px 0 0 0;">
                <a href="{{unsubscribe_url}}" style="color: #6b7280; text-decoration: none;">Unsubscribe</a>
              </p>
            </div>
          </div>
        `;
        
        return html;
      }
      
      autoSave() {
        if (this.elements.length > 0) {
          const templateData = {
            name: document.getElementById('templateName').value || 'Untitled Template',
            elements: this.elements,
            device: this.currentDevice,
            lastSaved: new Date().toISOString()
          };
          
          localStorage.setItem('rapidmarkt_template_draft', JSON.stringify(templateData));
        }
      }
      
      loadDraftIfExists() {
        const draft = localStorage.getItem('rapidmarkt_template_draft');
        if (draft) {
          try {
            const data = JSON.parse(draft);
            if (confirm('Found a saved draft. Would you like to restore it?')) {
              this.elements = data.elements || [];
              document.getElementById('templateName').value = data.name || 'Untitled Template';
              
              if (this.elements.length > 0) {
                document.getElementById('dropZone').style.display = 'none';
                this.rerenderCanvas();
              }
              
              this.saveState();
              this.showNotification('Draft restored!', 'success');
            }
          } catch (e) {
            console.error('Error loading draft:', e);
          }
        }
      }
      
      showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
          notification.classList.remove('show');
          setTimeout(() => notification.remove(), 300);
        }, 3000);
      }
      
      showLoading(show = true) {
        document.getElementById('loadingOverlay').classList.toggle('show', show);
      }
    }
    
    // Initialize template builder when DOM is ready
    let templateBuilder;
    
    function initializeBuilder() {
      templateBuilder = new CampaignBuilder();
      window.campaignBuilder = templateBuilder; // Make it globally accessible
      
      console.log('Template builder initialized');
    }
    
    // Ensure DOM is ready before initialization
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeBuilder);
    } else {
      initializeBuilder();
    }
    
    // Global functions with safety checks
    function switchTab(tabName) {
      console.log('switchTab called with:', tabName);
      if (templateBuilder && templateBuilder.switchTab) {
        templateBuilder.switchTab(tabName);
      } else {
        console.error('Template builder not initialized');
      }
    }
    
    function undoAction() {
      templateBuilder.undo();
    }
    
    function redoAction() {
      templateBuilder.redo();
    }
    
    function zoomOut() {
      templateBuilder.zoom = Math.max(0.5, templateBuilder.zoom - 0.1);
      document.getElementById('canvasContainer').style.transform = `scale(${templateBuilder.zoom})`;
    }
    
    function zoomIn() {
      templateBuilder.zoom = Math.min(2, templateBuilder.zoom + 0.1);
      document.getElementById('canvasContainer').style.transform = `scale(${templateBuilder.zoom})`;
    }
    
    function toggleGrid() {
      const canvas = document.getElementById('canvasContent');
      canvas.style.backgroundImage = canvas.style.backgroundImage ? '' : 
        'linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)';
      canvas.style.backgroundSize = canvas.style.backgroundSize ? '' : '20px 20px';
    }
    
    function clearCanvas() {
      templateBuilder.clearCanvas();
    }
    
    function previewTemplate() {
      const html = templateBuilder.generateTemplateHTML();
      const previewWindow = window.open('', '_blank');
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Template Preview</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
        </head>
        <body style="margin: 0; background: #f3f4f6; padding: 20px;">
          ${html}
        </body>
        </html>
      `);
      previewWindow.document.close();
    }
    
    function previewCampaign() {
      previewTemplate();
    }
    
    function publishCampaign() {
      saveTemplate(false);
    }
    
    function generateWithAI() {
      switchTab('ai');
    }
    
    function saveAsDraft() {
      saveTemplate(true);
    }
    
    function saveTemplate(isDraft = false) {
      const templateName = document.getElementById('templateName').value.trim();
      
      if (!templateName) {
        templateBuilder.showNotification('Please enter a template name', 'error');
        return;
      }
      
      if (templateBuilder.elements.length === 0) {
        templateBuilder.showNotification('Please add at least one component', 'error');
        return;
      }
      
      templateBuilder.showLoading(true);
      
      // Prepare form data
      const html = templateBuilder.generateTemplateHTML();
      const subject = templateName; // Default to template name
      
      document.getElementById('templateFormName').value = templateName;
      document.getElementById('templateFormSubject').value = subject;
      document.getElementById('templateFormBody').value = html;
      document.getElementById('templateFormStatus').value = isDraft ? 'draft' : 'active';
      document.getElementById('templateFormDesignConfig').value = JSON.stringify({
        builderVersion: '2.0',
        device: templateBuilder.currentDevice,
        zoom: templateBuilder.zoom
      });
      document.getElementById('templateFormVariables').value = JSON.stringify(
        templateBuilder.elements.map(el => el.content).flat()
      );
      document.getElementById('templateFormContentBlocks').value = JSON.stringify(templateBuilder.elements);
      
      // Submit form
      document.getElementById('templateForm').submit();
      
      // Clear draft from localStorage
      localStorage.removeItem('rapidmarkt_template_draft');
    }
    
    function generateWithAI() {
      const prompt = document.getElementById('aiPrompt').value.trim();
      const templateType = document.getElementById('templateType').value;
      const industry = document.getElementById('industry').value;
      const tone = document.getElementById('tone').value;
      
      if (!prompt) {
        templateBuilder.showNotification('Please describe your template', 'error');
        return;
      }
      
      templateBuilder.showLoading(true);
      
      // Simulate AI generation (replace with actual API call)
      setTimeout(() => {
        // Generate AI components based on prompt
        const aiComponents = generateAIComponents(prompt, templateType, industry, tone);
        
        // Clear existing elements
        templateBuilder.elements = [];
        
        // Add AI generated components
        aiComponents.forEach(componentType => {
          templateBuilder.addComponent(componentType);
        });
        
        // Hide drop zone
        document.getElementById('dropZone').style.display = 'none';
        
        templateBuilder.showLoading(false);
        templateBuilder.showNotification('AI template generated successfully!', 'success');
        
        // Switch to components tab
        switchTab('components');
      }, 2000);
    }
    
    function generateAIComponents(prompt, templateType, industry, tone) {
      // AI logic to determine components based on input
      const components = ['header'];
      
      if (prompt.toLowerCase().includes('hero') || prompt.toLowerCase().includes('banner')) {
        components.push('hero-section');
      }
      
      if (prompt.toLowerCase().includes('product') || templateType === 'promotional') {
        components.push('product-showcase');
      }
      
      if (prompt.toLowerCase().includes('testimonial') || prompt.toLowerCase().includes('review')) {
        components.push('testimonial');
      }
      
      if (prompt.toLowerCase().includes('form') || prompt.toLowerCase().includes('contact')) {
        components.push('form');
      }
      
      // Always add some text and a button
      components.push('text-block', 'button');
      
      return components;
    }
    
    function loadTemplate(templateType) {
      templateBuilder.showLoading(true);
      
      // Simulate loading predefined template
      setTimeout(() => {
        let components = [];
        
        switch (templateType) {
          case 'welcome-series':
            components = ['header', 'hero-section', 'text-block', 'button'];
            break;
          case 'product-announcement':
            components = ['header', 'hero-section', 'product-showcase', 'text-block', 'button'];
            break;
          case 'sale-promotion':
            components = ['header', 'hero-section', 'product-showcase', 'testimonial', 'button'];
            break;
        }
        
        // Clear existing
        templateBuilder.elements = [];
        
        // Add template components
        components.forEach(componentType => {
          templateBuilder.addComponent(componentType);
        });
        
        document.getElementById('dropZone').style.display = 'none';
        
        templateBuilder.showLoading(false);
        templateBuilder.showNotification('Template loaded successfully!', 'success');
      }, 1000);
    }
    
    // Auto-save every 30 seconds
    setInterval(() => {
      templateBuilder.autoSave();
    }, 30000);
    
    // Save before unload
    window.addEventListener('beforeunload', (e) => {
      templateBuilder.autoSave();
    });
  </script>
</body>
</html>