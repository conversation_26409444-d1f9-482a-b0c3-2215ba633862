<!DOCTYPE html>
<html lang="en">
<head>
  <title>RapidMarkt - Enhanced Template Builder</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  
  <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
  <%= javascript_importmap_tags %>
  
  <!-- Enhanced Styles for Template Builder -->
  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --glass-bg: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON>o, 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--primary-gradient);
      min-height: 100vh;
      overflow-x: hidden;
    }

    .builder-container {
      display: flex;
      height: 100vh;
      background: var(--primary-gradient);
    }

    .glass-panel {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    }

    .sidebar {
      width: 320px;
      padding: 20px;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }

    .sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
      background: transparent;
    }

    .sidebar::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    .main-canvas {
      flex: 1;
      padding: 20px;
      display: flex;
      flex-direction: column;
    }

    .canvas-header {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
      padding: 15px 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: between;
      align-items: center;
      color: white;
    }

    .canvas-content {
      flex: 1;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
    }

    .component-library {
      width: 350px;
      padding: 20px;
      overflow-y: auto;
    }

    .tab-container {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
      margin-bottom: 20px;
      overflow: hidden;
    }

    .tab-nav {
      display: flex;
      background: rgba(255, 255, 255, 0.1);
    }

    .tab-btn {
      flex: 1;
      padding: 12px 16px;
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.7);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .tab-btn.active {
      background: var(--secondary-gradient);
      color: white;
    }

    .tab-btn:hover:not(.active) {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }

    .tab-content {
      padding: 20px;
      color: white;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
    }

    .component-card {
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      border: 1px solid var(--glass-border);
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 15px;
      cursor: grab;
      transition: all 0.3s ease;
      color: white;
    }

    .component-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
    }

    .component-card:active {
      cursor: grabbing;
    }

    .component-preview {
      width: 100%;
      height: 60px;
      background: var(--secondary-gradient);
      border-radius: 8px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .drop-zone {
      min-height: 400px;
      border: 2px dashed #e5e7eb;
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      transition: all 0.3s ease;
      background: #f9fafb;
    }

    .drop-zone.drag-over {
      border-color: #667eea;
      background: #f0f4ff;
      transform: scale(1.02);
    }

    .ai-form {
      space-y: 15px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
    }

    .form-input, .form-select, .form-textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .form-input::placeholder, .form-textarea::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
      outline: none;
      border-color: #f093fb;
      box-shadow: 0 0 0 3px rgba(240, 147, 251, 0.2);
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: var(--secondary-gradient);
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(240, 147, 251, 0.4);
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .search-box {
      position: relative;
      margin-bottom: 20px;
    }

    .search-input {
      width: 100%;
      padding: 12px 40px 12px 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 14px;
    }

    .search-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255, 255, 255, 0.6);
    }

    .floating-toolbar {
      position: absolute;
      top: 20px;
      right: 20px;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 12px;
      padding: 10px;
      display: flex;
      gap: 10px;
      z-index: 10;
    }

    .toolbar-btn {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .toolbar-btn:hover {
      background: var(--secondary-gradient);
      transform: scale(1.1);
    }

    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 1000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      background: var(--success-gradient);
    }

    .notification.error {
      background: var(--secondary-gradient);
    }

    @media (max-width: 1024px) {
      .builder-container {
        flex-direction: column;
        height: auto;
      }
      
      .sidebar, .component-library {
        width: 100%;
        max-height: 300px;
      }
      
      .main-canvas {
        min-height: 500px;
      }
    }
  </style>
</head>
<body>
  <div class="builder-container">
    <!-- Left Sidebar - Template Types & AI Assistant -->
    <div class="sidebar glass-panel">
      <div class="tab-container">
        <div class="tab-nav">
          <button class="tab-btn active" onclick="switchTab('types')">📋 Types</button>
          <button class="tab-btn" onclick="switchTab('ai')">🤖 AI</button>
        </div>
        
        <div class="tab-content">
          <!-- Template Types Tab -->
          <div id="types-tab" class="tab-pane active">
            <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">Choose Template Type</h3>
            
            <div class="component-card" draggable="true" data-component="email-template">
              <div class="component-preview">📧</div>
              <h4 style="margin: 0 0 5px 0; font-size: 14px; font-weight: 600;">Email Campaign</h4>
              <p style="margin: 0; font-size: 12px; opacity: 0.8;">Newsletter, promotional, transactional</p>
            </div>
            
            <div class="component-card" draggable="true" data-component="landing-page">
              <div class="component-preview">🌐</div>
              <h4 style="margin: 0 0 5px 0; font-size: 14px; font-weight: 600;">Landing Page</h4>
              <p style="margin: 0; font-size: 12px; opacity: 0.8;">Lead capture, product launch</p>
            </div>
            
            <div class="component-card" draggable="true" data-component="social-media">
              <div class="component-preview">📱</div>
              <h4 style="margin: 0 0 5px 0; font-size: 14px; font-weight: 600;">Social Media</h4>
              <p style="margin: 0; font-size: 12px; opacity: 0.8;">Instagram, Facebook, LinkedIn</p>
            </div>
            
            <div class="component-card" draggable="true" data-component="blog-post">
              <div class="component-preview">✍️</div>
              <h4 style="margin: 0 0 5px 0; font-size: 14px; font-weight: 600;">Blog Post</h4>
              <p style="margin: 0; font-size: 12px; opacity: 0.8;">Article, tutorial, announcement</p>
            </div>
          </div>
          
          <!-- AI Assistant Tab -->
          <div id="ai-tab" class="tab-pane">
            <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">AI Template Generator</h3>
            
            <div class="ai-form">
              <div class="form-group">
                <label class="form-label">Describe your template</label>
                <textarea class="form-textarea" rows="4" placeholder="E.g., Create a modern newsletter for a tech startup with hero section, product features, and call-to-action..."></textarea>
              </div>
              
              <div class="form-group">
                <label class="form-label">Industry</label>
                <select class="form-select">
                  <option value="general">General</option>
                  <option value="tech">Technology</option>
                  <option value="ecommerce">E-commerce</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="finance">Finance</option>
                  <option value="education">Education</option>
                  <option value="nonprofit">Non-profit</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Tone</label>
                <select class="form-select">
                  <option value="professional">Professional</option>
                  <option value="friendly">Friendly</option>
                  <option value="casual">Casual</option>
                  <option value="formal">Formal</option>
                  <option value="playful">Playful</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
              
              <button class="btn btn-primary" style="width: 100%;" onclick="generateAITemplate()">
                <span class="loading-spinner" style="display: none;"></span>
                ✨ Generate Template
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Canvas Area -->
    <div class="main-canvas">
      <!-- Canvas Header -->
      <div class="canvas-header">
        <div>
          <h2 style="margin: 0; font-size: 20px; font-weight: 600;">Template Builder</h2>
          <p style="margin: 5px 0 0 0; opacity: 0.8; font-size: 14px;">Drag components to build your template</p>
        </div>
        <div style="display: flex; gap: 10px;">
          <button class="btn btn-secondary" onclick="previewTemplate()">👁️ Preview</button>
          <button class="btn btn-primary" onclick="saveTemplate()">💾 Save</button>
        </div>
      </div>
      
      <!-- Canvas Content -->
      <div class="canvas-content">
        <!-- Floating Toolbar -->
        <div class="floating-toolbar">
          <button class="toolbar-btn" onclick="undoAction()" title="Undo">↶</button>
          <button class="toolbar-btn" onclick="redoAction()" title="Redo">↷</button>
          <button class="toolbar-btn" onclick="zoomOut()" title="Zoom Out">🔍-</button>
          <button class="toolbar-btn" onclick="zoomIn()" title="Zoom In">🔍+</button>
          <button class="toolbar-btn" onclick="toggleGrid()" title="Toggle Grid">⊞</button>
        </div>
        
        <!-- Drop Zone -->
        <div class="drop-zone" id="canvas-drop-zone">
          <div style="color: #9ca3af; font-size: 48px; margin-bottom: 20px;">📄</div>
          <h3 style="color: #374151; margin: 0 0 10px 0; font-size: 24px;">Start Building Your Template</h3>
          <p style="color: #6b7280; margin: 0 0 20px 0; font-size: 16px;">Drag components from the library or use AI to generate content</p>
          <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
            <button class="btn btn-secondary" onclick="switchTab('types'); document.querySelector('.sidebar').scrollIntoView();">📦 Browse Components</button>
            <button class="btn btn-primary" onclick="switchTab('ai'); document.querySelector('.sidebar').scrollIntoView();">🤖 Generate with AI</button>
          </div>
        </div>
      </div>
    </div>
