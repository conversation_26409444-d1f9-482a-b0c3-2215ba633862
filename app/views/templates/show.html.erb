<div class="min-h-full">
  <!-- Breadcrumb -->
  <nav class="flex" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center space-x-4">
      <li>
        <div>
          <%= link_to dashboard_path, class: "text-gray-400 hover:text-gray-500" do %>
            <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
            </svg>
            <span class="sr-only">Home</span>
          <% end %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
          <%= link_to "Templates", templates_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
          <span class="ml-4 text-sm font-medium text-gray-500"><%= @template.name %></span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Page header -->
  <div class="mt-8">
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          <%= @template.name %>
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          <%= @template.subject %>
        </p>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
        <%= link_to edit_template_path(@template), 
            class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
          </svg>
          Edit
        <% end %>
        
        <%= link_to preview_template_path(@template), 
            target: "_blank",
            class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12z" clip-rule="evenodd" />
          </svg>
          Preview
        <% end %>
        
        <%= link_to duplicate_template_path(@template), 
            data: { turbo_method: :post },
            class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
            <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
          </svg>
          Duplicate
        <% end %>
        
        <%= link_to template_path(@template), 
            data: { turbo_method: :delete, turbo_confirm: 'Are you sure you want to delete this template? This action cannot be undone.' },
            class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" do %>
          <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l1.293-1.293z" clip-rule="evenodd" />
          </svg>
          Delete
        <% end %>
      </div>
    </div>
  </div>

  <!-- Template Details -->
  <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Template Information -->
    <div class="lg:col-span-2">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Template Information</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about this email template.</p>
        </div>
        <div class="border-t border-gray-200">
          <dl>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Template name</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.name %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Subject line</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.subject %></dd>
            </div>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Last updated</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.updated_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
            </div>
          </dl>
        </div>
      </div>
      
      <!-- Email Content Preview -->
      <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Email Content</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Preview of the email template content.</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
          <% if @template.body.present? %>
            <div class="prose max-w-none">
              <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <iframe srcdoc="<%= html_escape(@template.body) %>" 
                        class="w-full h-96 border-0 rounded"
                        sandbox="allow-same-origin">
                </iframe>
              </div>
            </div>
            
            <div class="mt-4">
              <details class="group">
                <summary class="flex cursor-pointer items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900">
                  <svg class="h-4 w-4 transition-transform group-open:rotate-90" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                  View HTML Source
                </summary>
                <div class="mt-2">
                  <pre class="bg-gray-100 p-4 rounded-lg text-xs overflow-x-auto"><code><%= @template.body %></code></pre>
                </div>
              </details>
            </div>
          <% else %>
            <p class="text-gray-500 italic">No content available for this template.</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Usage Statistics -->
    <div class="lg:col-span-1">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Usage Statistics</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">How this template is being used.</p>
        </div>
        <div class="border-t border-gray-200">
          <dl>
            <div class="bg-gray-50 px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Campaigns using this template</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900"><%= @template.campaigns.count %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Total emails sent</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900"><%= @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { sent_at: nil }).count %></dd>
            </div>
            <div class="bg-gray-50 px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Total emails opened</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900"><%= @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { opened_at: nil }).count %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Average open rate</dt>
              <dd class="mt-1 text-lg font-semibold text-gray-900">
                <% total_sent = @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { sent_at: nil }).count %>
                <% total_opened = @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { opened_at: nil }).count %>
                <% if total_sent > 0 %>
                  <%= number_to_percentage((total_opened.to_f / total_sent) * 100, precision: 1) %>
                <% else %>
                  <span class="text-gray-500">N/A</span>
                <% end %>
              </dd>
            </div>
          </dl>
        </div>
      </div>
      
      <!-- Recent Campaigns -->
      <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Campaigns</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Latest campaigns using this template.</p>
        </div>
        <div class="border-t border-gray-200">
          <% recent_campaigns = @template.campaigns.order(created_at: :desc).limit(5) %>
          <% if recent_campaigns.any? %>
            <ul class="divide-y divide-gray-200">
              <% recent_campaigns.each do |campaign| %>
                <li class="px-4 py-4">
                  <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                      <%= link_to campaign_path(campaign), class: "text-sm font-medium text-indigo-600 hover:text-indigo-500 truncate" do %>
                        <%= campaign.name %>
                      <% end %>
                      <p class="text-xs text-gray-500 mt-1">
                        <%= time_ago_in_words(campaign.created_at) %> ago
                      </p>
                    </div>
                    <div class="ml-4 flex-shrink-0">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= campaign.status == 'sent' ? 'bg-green-100 text-green-800' : campaign.status == 'scheduled' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' %>">
                        <%= campaign.status.humanize %>
                      </span>
                    </div>
                  </div>
                </li>
              <% end %>
            </ul>
            <div class="px-4 py-3 bg-gray-50">
              <%= link_to campaigns_path(template_id: @template.id), 
                  class: "text-sm text-indigo-600 hover:text-indigo-500" do %>
                View all campaigns using this template →
              <% end %>
            </div>
          <% else %>
            <div class="px-4 py-6 text-center">
              <p class="text-sm text-gray-500">No campaigns have used this template yet.</p>
              <%= link_to new_campaign_path(template_id: @template.id), 
                  class: "mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                Create Campaign
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>