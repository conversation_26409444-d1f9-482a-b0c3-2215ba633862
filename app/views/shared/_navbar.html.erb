<!-- Main Navigation -->
<nav class="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50 backdrop-blur-sm bg-white/95">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      <!-- Left Section: Logo and Primary Navigation -->
      <div class="flex">
        <!-- Logo -->
        <div class="flex-shrink-0 flex items-center">
          <%= link_to root_path, class: "flex items-center space-x-3 text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105" do %>
            <div class="relative">
              <svg class="h-10 w-10 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
              <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-lg blur-lg opacity-30 animate-pulse"></div>
            </div>
            <span class="font-extrabold tracking-tight">RapidMarkt</span>
          <% end %>
        </div>
        
        <!-- Primary Navigation Links (Desktop) -->
        <div class="hidden lg:ml-8 lg:flex lg:space-x-1">
          <%= render 'shared/navbar_link', path: dashboard_path, label: 'Dashboard', icon: 'dashboard' %>
          <%= render 'shared/navbar_link', path: campaigns_path, label: 'Campaigns', icon: 'campaigns' %>
          <%= render 'shared/navbar_link', path: contacts_path, label: 'Contacts', icon: 'contacts' %>
          <%= render 'shared/navbar_link', path: templates_path, label: 'Templates', icon: 'templates' %>
          <%= render 'shared/navbar_link', path: analytics_path, label: 'Analytics', icon: 'analytics' %>
        </div>
      </div>
      
      <!-- Right Section: Secondary Navigation and User Menu -->
      <div class="flex items-center space-x-4">
        <!-- Secondary Navigation (Desktop) -->
        <div class="hidden md:flex md:items-center md:space-x-2">
          <!-- Notifications -->
          <div class="relative" data-controller="dropdown">
            <button type="button"
                    class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full transition-all duration-200 relative"
                    data-action="click->dropdown#toggle"
                    aria-expanded="false"
                    aria-haspopup="true"
                    title="Notifications">
              <span class="sr-only">View notifications</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z" />
              </svg>
              <!-- Notification Badge -->
              <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
            </button>
            
            <div class="origin-top-right absolute right-0 mt-2 w-80 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50"
                 data-dropdown-target="menu"
                 role="menu"
                 aria-orientation="vertical">
              <div class="py-2" role="none">
                <div class="px-4 py-2 border-b border-gray-100">
                  <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                </div>
                <div class="max-h-64 overflow-y-auto">
                  <!-- Notification items would go here -->
                  <div class="px-4 py-3 text-sm text-gray-500 text-center">
                    No new notifications
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Quick Actions -->
          <div class="relative" data-controller="dropdown">
            <button type="button"
                    class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full transition-all duration-200"
                    data-action="click->dropdown#toggle"
                    aria-expanded="false"
                    aria-haspopup="true"
                    title="Quick Actions">
              <span class="sr-only">Quick actions</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </button>
            
            <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50"
                 data-dropdown-target="menu"
                 role="menu"
                 aria-orientation="vertical">
              <div class="py-2" role="none">
                <div class="px-4 py-2 border-b border-gray-100">
                  <h3 class="text-sm font-semibold text-gray-900">Quick Actions</h3>
                </div>
                <%= link_to new_campaign_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  New Campaign
                <% end %>
                <%= link_to new_contact_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Add Contact
                <% end %>
                <%= link_to new_template_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  New Template
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
        <!-- User Menu -->
        <div class="relative" data-controller="dropdown">
          <button type="button"
                  class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-all duration-200 group"
                  data-action="click->dropdown#toggle"
                  aria-expanded="false"
                  aria-haspopup="true"
                  title="User menu">
            <span class="sr-only">Open user menu</span>
            
            <!-- User Avatar -->
            <div class="h-8 w-8 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm shadow-md">
              <%= current_user.first_name&.first || current_user.email.first.upcase %>
            </div>
            
            <!-- User Info (Desktop) -->
            <div class="hidden md:block text-left">
              <div class="text-sm font-medium text-gray-900">
                <%= current_user.first_name || current_user.email.split('@').first.titleize %>
              </div>
              <div class="text-xs text-gray-500">
                <%= current_user.account&.name || 'Personal Account' %>
              </div>
            </div>
            
            <!-- Dropdown Arrow -->
            <svg class="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <div class="origin-top-right absolute right-0 mt-2 w-64 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50"
               data-dropdown-target="menu"
               role="menu"
               aria-orientation="vertical">
            <div class="py-2" role="none">
              <!-- User Info Header -->
              <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                  <div class="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-semibold">
                    <%= current_user.first_name&.first || current_user.email.first.upcase %>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">
                      <%= current_user.first_name || current_user.email.split('@').first.titleize %>
                    </div>
                    <div class="text-xs text-gray-500">
                      <%= current_user.email %>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Account Management -->
              <div class="py-1">
                <%= link_to account_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Account Settings
                <% end %>
                <%= link_to team_account_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Team Management
                <% end %>
                <%= link_to billing_account_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  Billing & Subscription
                <% end %>
              </div>
              
              <hr class="my-1">
              
              <!-- Additional Options -->
              <div class="py-1">
                <%= link_to brand_voices_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  Brand Voices
                <% end %>
                <%= link_to automations_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  Automations
                <% end %>
              </div>
              
              <hr class="my-1">
              
              <!-- Sign Out -->
              <div class="py-1">
                <%= link_to destroy_user_session_path, 
                    data: { turbo_method: :delete, confirm: "Are you sure you want to sign out?" },
                    class: "flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50 transition-colors duration-200", 
                    role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign Out
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Mobile menu button -->
        <div class="flex items-center lg:hidden">
          <button type="button" 
                  class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 transition-all duration-200" 
                  data-controller="mobile-menu"
                  data-action="click->mobile-menu#toggle"
                  aria-controls="mobile-menu" 
                  aria-expanded="false">
            <span class="sr-only">Open main menu</span>
            <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Mobile menu -->
  <div class="lg:hidden hidden" data-mobile-menu-target="menu" id="mobile-menu">
    <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
      <!-- Primary Navigation -->
      <%= render 'shared/mobile_navbar_link', path: dashboard_path, label: 'Dashboard', icon: 'dashboard' %>
      <%= render 'shared/mobile_navbar_link', path: campaigns_path, label: 'Campaigns', icon: 'campaigns' %>
      <%= render 'shared/mobile_navbar_link', path: contacts_path, label: 'Contacts', icon: 'contacts' %>
      <%= render 'shared/mobile_navbar_link', path: templates_path, label: 'Templates', icon: 'templates' %>
      <%= render 'shared/mobile_navbar_link', path: analytics_path, label: 'Analytics', icon: 'analytics' %>
      
      <!-- Secondary Navigation -->
      <div class="border-t border-gray-200 pt-3 mt-3">
        <%= render 'shared/mobile_navbar_link', path: brand_voices_path, label: 'Brand Voices', icon: 'brand_voices' %>
        <%= render 'shared/mobile_navbar_link', path: automations_path, label: 'Automations', icon: 'automations' %>
      </div>
      
      <!-- Quick Actions -->
      <div class="border-t border-gray-200 pt-3 mt-3">
        <div class="px-3 py-2">
          <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Quick Actions</h3>
        </div>
        <%= render 'shared/mobile_navbar_link', path: new_campaign_path, label: 'New Campaign', icon: 'new_campaign' %>
        <%= render 'shared/mobile_navbar_link', path: new_contact_path, label: 'Add Contact', icon: 'new_contact' %>
        <%= render 'shared/mobile_navbar_link', path: new_template_path, label: 'New Template', icon: 'new_template' %>
      </div>
    </div>
  </div>
</nav>