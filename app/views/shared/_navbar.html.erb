<!-- Main Navigation -->
<nav class="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50 backdrop-blur-sm bg-white/95">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      <!-- Left Section: Logo and Primary Navigation -->
      <div class="flex">
        <!-- Logo -->
        <div class="flex-shrink-0 flex items-center">
          <%= link_to root_path, class: "flex items-center space-x-3 text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105" do %>
            <div class="relative">
              <svg class="h-10 w-10 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
              <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-lg blur-lg opacity-30 animate-pulse"></div>
            </div>
            <span class="font-extrabold tracking-tight">RapidMarkt</span>
          <% end %>
        </div>
        
        <!-- Primary Navigation Links (Desktop) -->
        <div class="hidden lg:ml-8 lg:flex lg:space-x-1">
          <%= render 'shared/navbar_link', path: dashboard_path, label: 'Dashboard', icon: 'dashboard' %>
          <%= render 'shared/navbar_link', path: campaigns_path, label: 'Campaigns', icon: 'campaigns' %>
          <%= render 'shared/navbar_link', path: contacts_path, label: 'Contacts', icon: 'contacts' %>
          <%= render 'shared/navbar_link', path: templates_path, label: 'Templates', icon: 'templates' %>
          <%= render 'shared/navbar_link', path: analytics_path, label: 'Analytics', icon: 'analytics' %>
        </div>
      </div>
      
      <!-- Right Section: Secondary Navigation and User Menu -->
      <div class="flex items-center space-x-4">
        <!-- Secondary Navigation (Desktop) -->
        <div class="hidden md:flex md:items-center md:space-x-2">
          <!-- Notifications -->
          <div class="relative" data-controller="dropdown">
            <button type="button"
                    class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full transition-all duration-200 relative group"
                    data-action="click->dropdown#toggle"
                    aria-expanded="false"
                    aria-haspopup="true"
                    title="Notifications">
              <span class="sr-only">View notifications</span>
              <svg class="h-6 w-6 group-hover:scale-110 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z" />
              </svg>
              <!-- Notification Badge -->
              <span class="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse shadow-lg">5</span>
            </button>
            
            <div class="origin-top-right absolute right-0 mt-2 w-96 rounded-xl shadow-2xl bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50 border border-gray-100"
                 data-dropdown-target="menu"
                 role="menu"
                 aria-orientation="vertical">
              <div class="py-2" role="none">
                <div class="px-4 py-3 border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
                  <div class="flex items-center justify-between">
                    <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                    <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium">Mark all read</button>
                  </div>
                </div>
                <div class="max-h-80 overflow-y-auto">
                  <!-- Sample Notification Items -->
                  <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-50 transition-colors duration-200">
                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                          <svg class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">Campaign "Summer Sale" completed</p>
                        <p class="text-xs text-gray-500 mt-1">Sent to 1,234 contacts with 23% open rate</p>
                        <p class="text-xs text-gray-400 mt-1">2 minutes ago</p>
                      </div>
                      <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-50 transition-colors duration-200">
                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <svg class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">New contact added</p>
                        <p class="text-xs text-gray-500 mt-1"><EMAIL> joined your mailing list</p>
                        <p class="text-xs text-gray-400 mt-1">15 minutes ago</p>
                      </div>
                      <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-50 transition-colors duration-200">
                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                          <svg class="h-4 w-4 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                        </div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">Usage limit warning</p>
                        <p class="text-xs text-gray-500 mt-1">You've used 85% of your monthly email quota</p>
                        <p class="text-xs text-gray-400 mt-1">1 hour ago</p>
                      </div>
                      <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div class="px-4 py-3 hover:bg-gray-50 transition-colors duration-200">
                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                          <svg class="h-4 w-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                        </div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">Automation triggered</p>
                        <p class="text-xs text-gray-500 mt-1">Welcome series started for 5 new subscribers</p>
                        <p class="text-xs text-gray-400 mt-1">3 hours ago</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="px-4 py-3 border-t border-gray-100 bg-gray-50">
                  <button class="w-full text-center text-sm text-indigo-600 hover:text-indigo-800 font-medium">View all notifications</button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Quick Actions -->
          <div class="relative" data-controller="dropdown">
            <button type="button"
                    class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full transition-all duration-200"
                    data-action="click->dropdown#toggle"
                    aria-expanded="false"
                    aria-haspopup="true"
                    title="Quick Actions">
              <span class="sr-only">Quick actions</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </button>
            
            <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50"
                 data-dropdown-target="menu"
                 role="menu"
                 aria-orientation="vertical">
              <div class="py-2" role="none">
                <div class="px-4 py-2 border-b border-gray-100">
                  <h3 class="text-sm font-semibold text-gray-900">Quick Actions</h3>
                </div>
                <%= link_to new_campaign_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  New Campaign
                <% end %>
                <%= link_to new_contact_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Add Contact
                <% end %>
                <%= link_to new_template_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200", role: "menuitem" do %>
                  <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  New Template
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
        <!-- User Menu -->
        <div class="relative" data-controller="dropdown">
          <button type="button"
                  class="flex items-center space-x-3 p-2 rounded-xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 group border border-transparent hover:border-indigo-200 shadow-sm hover:shadow-md"
                  data-action="click->dropdown#toggle"
                  aria-expanded="false"
                  aria-haspopup="true"
                  title="User menu">
            <span class="sr-only">Open user menu</span>
            
            <!-- User Avatar -->
            <div class="relative">
              <% if current_user.respond_to?(:avatar) && current_user.avatar.attached? %>
                <%= image_tag current_user.avatar, class: "h-10 w-10 rounded-full object-cover ring-2 ring-white shadow-lg" %>
              <% else %>
                <div class="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-sm shadow-lg ring-2 ring-white">
                  <%= current_user.first_name&.first || current_user.email.first.upcase %>
                </div>
              <% end %>
              <!-- Online Status Indicator -->
              <div class="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-green-400 border-2 border-white rounded-full"></div>
            </div>
            
            <!-- User Info (Desktop) -->
            <div class="hidden md:block text-left">
              <div class="text-sm font-semibold text-gray-900 group-hover:text-indigo-700 transition-colors duration-200">
                <%= current_user.full_name %>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">
                  <%= current_user.account&.name || 'Personal Account' %>
                </span>
                <span class="text-xs px-2 py-0.5 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 rounded-full font-medium">
                  <%= current_user.role.titleize %>
                </span>
              </div>
            </div>
            
            <!-- Dropdown Arrow -->
            <svg class="h-4 w-4 text-gray-400 group-hover:text-indigo-600 transition-all duration-200 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <div class="origin-top-right absolute right-0 mt-2 w-72 rounded-xl shadow-2xl bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50 border border-gray-100"
               data-dropdown-target="menu"
               role="menu"
               aria-orientation="vertical">
            <div class="py-2" role="none">
              <!-- User Info Header -->
              <div class="px-4 py-4 border-b border-gray-100 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50">
                <div class="flex items-center space-x-4">
                  <div class="relative">
                    <% if current_user.respond_to?(:avatar) && current_user.avatar.attached? %>
                      <%= image_tag current_user.avatar, class: "h-12 w-12 rounded-full object-cover ring-2 ring-white shadow-lg" %>
                    <% else %>
                      <div class="h-12 w-12 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg shadow-lg ring-2 ring-white">
                        <%= current_user.first_name&.first || current_user.email.first.upcase %>
                      </div>
                    <% end %>
                    <!-- Online Status Indicator -->
                    <div class="absolute -bottom-0.5 -right-0.5 h-4 w-4 bg-green-400 border-2 border-white rounded-full"></div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-semibold text-gray-900">
                      <%= current_user.full_name %>
                    </div>
                    <div class="text-xs text-gray-600 truncate">
                      <%= current_user.email %>
                    </div>
                    <div class="flex items-center space-x-2 mt-1">
                      <span class="text-xs text-gray-500">
                        <%= current_user.account&.name || 'Personal Account' %>
                      </span>
                      <span class="text-xs px-2 py-0.5 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 rounded-full font-medium">
                        <%= current_user.role.titleize %>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Account Management -->
              <div class="px-3 py-3">
                <div class="text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 flex items-center">
                  <svg class="mr-2 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                  </svg>
                  Account Management
                </div>
                <%= link_to account_path, class: "group flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 rounded-lg transition-all duration-200 mb-1", role: "menuitem" do %>
                  <div class="mr-3 h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors duration-200">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <span class="font-medium">Account Settings</span>
                <% end %>
                <%= link_to team_account_path, class: "group flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 rounded-lg transition-all duration-200 mb-1", role: "menuitem" do %>
                  <div class="mr-3 h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors duration-200">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                  </div>
                  <span class="font-medium">Team Management</span>
                <% end %>
                <%= link_to billing_account_path, class: "group flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 rounded-lg transition-all duration-200", role: "menuitem" do %>
                  <div class="mr-3 h-5 w-5 text-gray-400 group-hover:text-indigo-500 transition-colors duration-200">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                  </div>
                  <span class="font-medium">Billing & Subscription</span>
                <% end %>
              </div>
              
              <div class="border-t border-gray-100 my-2"></div>
              
              <!-- Additional Options -->
              <div class="px-3 py-3">
                <div class="text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 flex items-center">
                  <svg class="mr-2 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"></path>
                  </svg>
                  Tools & Features
                </div>
                <%= link_to brand_voices_path, class: "group flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 rounded-lg transition-all duration-200 mb-1", role: "menuitem" do %>
                  <div class="mr-3 h-5 w-5 text-gray-400 group-hover:text-purple-500 transition-colors duration-200">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </div>
                  <span class="font-medium">Brand Voices</span>
                <% end %>
                <%= link_to automations_path, class: "group flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 rounded-lg transition-all duration-200", role: "menuitem" do %>
                  <div class="mr-3 h-5 w-5 text-gray-400 group-hover:text-purple-500 transition-colors duration-200">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <span class="font-medium">Automations</span>
                <% end %>
              </div>
              
              <div class="border-t border-gray-100 my-2"></div>
              
              <!-- Sign Out -->
              <div class="px-3 py-3">
                <%= link_to destroy_user_session_path, 
                    data: { turbo_method: :delete, confirm: "Are you sure you want to sign out?" },
                    class: "group flex items-center px-3 py-2.5 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 rounded-lg transition-all duration-200", 
                    role: "menuitem" do %>
                  <div class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600 transition-colors duration-200">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </div>
                  <span class="font-semibold">Sign Out</span>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Mobile menu button -->
        <div class="flex items-center lg:hidden">
          <button type="button" 
                  class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 transition-all duration-200" 
                  data-controller="mobile-menu"
                  data-action="click->mobile-menu#toggle"
                  aria-controls="mobile-menu" 
                  aria-expanded="false">
            <span class="sr-only">Open main menu</span>
            <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Mobile menu -->
  <div class="fixed inset-0 z-50 hidden" data-mobile-menu-target="menu">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" data-action="click->mobile-menu#close"></div>
    
    <!-- Menu panel -->
    <div class="fixed inset-y-0 right-0 max-w-sm w-full bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-in-out" data-mobile-menu-target="panel">
      <div class="flex flex-col h-full">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50">
          <div class="flex items-center space-x-3">
            <div class="h-8 w-8 rounded-lg bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center">
              <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h2 class="text-lg font-bold text-gray-900">RapidMarkt</h2>
          </div>
          <button type="button" class="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-white/50 transition-all duration-200" data-action="click->mobile-menu#close">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- User Info -->
        <div class="px-6 py-4 border-b border-gray-100">
          <div class="flex items-center space-x-3">
            <div class="relative">
              <% if current_user.respond_to?(:avatar) && current_user.avatar.attached? %>
                <%= image_tag current_user.avatar, class: "h-10 w-10 rounded-full object-cover ring-2 ring-white shadow-md" %>
              <% else %>
                <div class="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold shadow-md ring-2 ring-white">
                  <%= current_user.first_name&.first || current_user.email.first.upcase %>
                </div>
              <% end %>
              <div class="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-green-400 border-2 border-white rounded-full"></div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-semibold text-gray-900 truncate">
                <%= current_user.full_name %>
              </div>
              <div class="text-xs text-gray-500 truncate">
                <%= current_user.email %>
              </div>
            </div>
          </div>
        </div>

        <!-- Content -->
        <div class="flex-1 overflow-y-auto py-2">
          <!-- Primary Navigation -->
          <div class="px-4 mb-6">
            <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 px-2 flex items-center">
              <svg class="mr-2 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-9 9a1 1 0 001.414 1.414L2 12.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-4.586l.293.293a1 1 0 001.414-1.414l-9-9z"></path>
              </svg>
              Navigation
            </h3>
            <div class="space-y-1">
               <%= render 'shared/mobile_navbar_link', path: dashboard_path, text: 'Dashboard', icon: 'dashboard' %>
               <%= render 'shared/mobile_navbar_link', path: campaigns_path, text: 'Campaigns', icon: 'campaigns' %>
               <%= render 'shared/mobile_navbar_link', path: contacts_path, text: 'Contacts', icon: 'contacts' %>
               <%= render 'shared/mobile_navbar_link', path: templates_path, text: 'Templates', icon: 'templates' %>
               <%= render 'shared/mobile_navbar_link', path: analytics_path, text: 'Analytics', icon: 'analytics' %>
             </div>
          </div>

          <!-- Secondary Navigation -->
          <div class="px-4 mb-6">
            <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 px-2 flex items-center">
              <svg class="mr-2 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
              </svg>
              Account
            </h3>
            <div class="space-y-1">
               <%= render 'shared/mobile_navbar_link', path: account_path, text: 'Account Settings', icon: 'settings' %>
               <%= render 'shared/mobile_navbar_link', path: team_account_path, text: 'Team Management', icon: 'team' %>
                <%= render 'shared/mobile_navbar_link', path: billing_account_path, text: 'Billing', icon: 'billing' %>
             </div>
          </div>

          <!-- Tools & Features -->
          <div class="px-4 mb-6">
            <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 px-2 flex items-center">
              <svg class="mr-2 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"></path>
              </svg>
              Tools & Features
            </h3>
            <div class="space-y-1">
               <%= render 'shared/mobile_navbar_link', path: brand_voices_path, text: 'Brand Voices', icon: 'brand_voices' %>
               <%= render 'shared/mobile_navbar_link', path: automations_path, text: 'Automations', icon: 'automations' %>
             </div>
          </div>

          <!-- Quick Actions -->
          <div class="px-4">
            <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 px-2 flex items-center">
              <svg class="mr-2 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
              </svg>
              Quick Actions
            </h3>
            <div class="space-y-2">
              <%= link_to new_campaign_path, class: "flex items-center justify-center px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-md" do %>
                <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                <span class="font-medium">New Campaign</span>
              <% end %>
              <%= link_to new_contact_path, class: "flex items-center justify-center px-4 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-md" do %>
                <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                <span class="font-medium">New Contact</span>
              <% end %>
              <%= link_to new_template_path, class: "flex items-center justify-center px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-md" do %>
                <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                <span class="font-medium">New Template</span>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="border-t border-gray-100 p-4 bg-gray-50">
          <%= link_to destroy_user_session_path, data: { turbo_method: :delete }, class: "flex items-center justify-center w-full px-4 py-3 text-red-600 hover:bg-red-50 hover:text-red-700 rounded-lg transition-all duration-200 font-medium" do %>
            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Sign Out
          <% end %>
        </div>
      </div>
    </div>
  </div>
</nav>