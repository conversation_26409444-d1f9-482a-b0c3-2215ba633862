<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
  <title>Enhanced Template Builder - RapidMarkt</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  
  <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
  <%= javascript_importmap_tags %>
  
  <!-- Enhanced Template Builder Styles -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-50: #eff6ff;
      --primary-100: #dbeafe;
      --primary-200: #bfdbfe;
      --primary-500: #3b82f6;
      --primary-600: #2563eb;
      --primary-700: #1d4ed8;
      --primary-900: #1e3a8a;
      
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
      
      --success-50: #ecfdf5;
      --success-500: #10b981;
      --warning-50: #fffbeb;
      --warning-500: #f59e0b;
      --error-50: #fef2f2;
      --error-500: #ef4444;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--gray-100);
      height: 100vh;
      overflow: hidden;
    }

    .builder-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .builder-header {
      background: white;
      border-bottom: 1px solid var(--gray-200);
      padding: 16px 24px;
      z-index: 1000;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 100%;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 24px;
      flex: 1;
    }

    .brand-section {
      flex-shrink: 0;
    }

    .brand-title {
      font-size: 18px;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0;
      background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .campaign-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .campaign-input {
      font-size: 16px;
      font-weight: 500;
      color: var(--gray-900);
      border: 1px solid var(--gray-300);
      background: white;
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.2s;
      min-width: 200px;
    }

    .campaign-input:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .platform-select {
      background: white;
      border: 1px solid var(--gray-300);
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 14px;
      color: var(--gray-700);
      min-width: 140px;
      cursor: pointer;
    }

    .header-center {
      flex-shrink: 0;
    }

    .device-preview {
      display: flex;
      background: var(--gray-100);
      border-radius: 8px;
      padding: 4px;
      gap: 2px;
    }

    .device-btn {
      background: transparent;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      color: var(--gray-600);
      cursor: pointer;
      transition: all 0.2s;
      font-weight: 500;
    }

    .device-btn.active {
      background: white;
      color: var(--primary-600);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .device-btn:hover:not(.active) {
      color: var(--gray-800);
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-shrink: 0;
    }

    .quick-actions {
      display: flex;
      gap: 4px;
    }

    .action-btn {
      background: var(--gray-100);
      border: none;
      padding: 8px 10px;
      border-radius: 6px;
      color: var(--gray-600);
      cursor: pointer;
      transition: all 0.2s;
      font-size: 14px;
      font-weight: 600;
    }

    .action-btn:hover {
      background: var(--gray-200);
      color: var(--gray-800);
    }

    .main-actions {
      display: flex;
      gap: 8px;
    }

    .btn-ai {
      background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn-ai:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .btn-preview {
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
    }

    .btn-save {
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
    }

    .btn-preview:hover, .btn-save:hover {
      background: var(--gray-200);
      border-color: var(--gray-400);
    }

    .btn-publish {
      background: linear-gradient(135deg, var(--success-500), #059669);
      color: white;
      border: none;
    }

    .btn-publish:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.2s;
      cursor: pointer;
      border: none;
      white-space: nowrap;
    }

    .btn-primary {
      background: var(--primary-600);
      color: white;
    }

    .btn-primary:hover {
      background: var(--primary-700);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: white;
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
    }

    .btn-secondary:hover {
      background: var(--gray-50);
      border-color: var(--gray-400);
    }

    .btn-success {
      background: var(--success-500);
      color: white;
    }

    .btn-success:hover {
      background: #059669;
      transform: translateY(-1px);
    }

    .builder-main {
      display: flex;
      flex: 1;
      overflow: hidden;
      height: calc(100vh - 80px);
    }

    .sidebar {
      background: white;
      border-right: 1px solid var(--gray-200);
      padding: 0;
      width: 300px;
      flex-shrink: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .canvas-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: var(--gray-100);
      overflow: hidden;
      position: relative;
      min-width: 0; /* Allow flex shrinking */
    }

    .canvas-container {
      flex: 1;
      overflow: auto;
      padding: 24px;
      position: relative;
    }

    .canvas-content {
      background: white;
      min-height: calc(100vh - 160px);
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      position: relative;
      width: 100%;
      max-width: none;
      padding: 32px;
      margin: 0 auto;
    }

    .floating-toolbar {
      position: absolute;
      top: 30px;
      right: 30px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      padding: 8px;
      display: flex;
      gap: 4px;
      z-index: 100;
    }

    .toolbar-btn {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;
      color: var(--gray-600);
      font-size: 14px;
    }

    .toolbar-btn:hover {
      background: var(--gray-100);
      color: var(--gray-800);
    }

    .drop-zone {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 500px;
      width: 100%;
      border: 2px dashed var(--gray-300);
      border-radius: 12px;
      background: var(--gray-50);
      text-align: center;
      transition: all 0.3s ease;
      margin: 0;
      cursor: pointer;
    }

    .drop-zone.drag-over {
      border-color: var(--primary-500);
      background: var(--primary-50);
    }

    .drop-zone-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.7;
    }

    .drop-zone-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--gray-700);
      margin: 0 0 12px 0;
    }

    .drop-zone-subtitle {
      font-size: 16px;
      color: var(--gray-500);
      margin: 0;
      max-width: 500px;
      line-height: 1.5;
    }

    .properties-panel {
      background: white;
      border-left: 1px solid var(--gray-200);
      width: 280px;
      flex-shrink: 0;
      overflow-y: auto;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .properties-header {
      padding: 20px;
      border-bottom: 1px solid var(--gray-200);
      background: var(--gray-50);
    }

    .properties-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 4px 0;
    }

    .properties-content {
      padding: 20px;
    }

    /* Sidebar content scrolling */
    .sidebar-content {
      flex: 1;
      overflow-y: auto;
    }

    .sidebar-header {
      padding: 20px;
      border-bottom: 1px solid var(--gray-200);
      background: var(--gray-50);
      flex-shrink: 0;
    }

    .sidebar-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 4px 0;
    }

    .sidebar-subtitle {
      font-size: 12px;
      color: var(--gray-600);
      margin: 0;
    }

    .tab-nav {
      display: flex;
      border-bottom: 1px solid var(--gray-200);
      background: white;
      flex-shrink: 0;
    }

    .tab-pane {
      display: none;
      padding: 20px;
    }

    .tab-pane.active {
      display: block;
    }

    .search-box {
      position: relative;
      margin-bottom: 20px;
    }

    .search-input {
      width: 100%;
      padding: 10px 40px 10px 12px;
      border: 1px solid var(--gray-300);
      border-radius: 6px;
      font-size: 14px;
    }

    .search-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--gray-400);
    }

    .component-section {
      margin-bottom: 24px;
    }

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-800);
      margin: 0 0 12px 0;
    }

    .component-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }

    .component-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 8px;
      border: 1px solid var(--gray-200);
      border-radius: 8px;
      cursor: grab;
      transition: all 0.2s;
      background: white;
      text-align: center;
    }

    .component-item:hover {
      border-color: var(--primary-300);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
      transform: translateY(-1px);
    }

    .component-item:active {
      cursor: grabbing;
    }

    .component-icon {
      font-size: 20px;
      margin-bottom: 6px;
    }

    .component-name {
      font-size: 11px;
      font-weight: 500;
      color: var(--gray-700);
      margin: 0;
    }

    .version-info {
      padding: 16px 20px;
      background: var(--gray-50);
      border-top: 1px solid var(--gray-200);
      font-size: 11px;
      color: var(--gray-500);
      text-align: center;
      flex-shrink: 0;
    }

    /* Enhanced Professional Styles */
    .subtitle {
      font-size: 12px;
      color: var(--gray-500);
      margin-left: 8px;
    }

    .campaign-name-input {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
      border: 1px solid var(--gray-300);
      background: white;
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.2s;
      min-width: 200px;
      margin-right: 12px;
    }

    .platform-dropdown {
      background: white;
      border: 1px solid var(--gray-300);
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 14px;
      color: var(--gray-700);
      min-width: 180px;
    }

    .btn-icon {
      background: var(--gray-100);
      border: 1px solid var(--gray-300);
      padding: 8px;
      border-radius: 6px;
      color: var(--gray-600);
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn-icon:hover {
      background: var(--gray-200);
      color: var(--gray-800);
    }

    .action-group {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    /* Platform-specific badges */
    .platform-badge {
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 4px;
      background: var(--gray-100);
      color: var(--gray-600);
      margin-top: 4px;
      display: inline-block;
    }

    .platform-badge.tiktok { background: #ff0050; color: white; }
    .platform-badge.instagram { background: #e4405f; color: white; }
    .platform-badge.youtube { background: #ff0000; color: white; }
    .platform-badge.linkedin { background: #0077b5; color: white; }
    .platform-badge.facebook { background: #1877f2; color: white; }
    .platform-badge.twitter { background: #1da1f2; color: white; }
    .platform-badge.universal { background: var(--primary-500); color: white; }

    /* Premium component styling */
    .component-item.premium {
      position: relative;
      border: 2px solid var(--primary-200);
      background: linear-gradient(135deg, var(--primary-50), white);
    }

    .component-item.premium::after {
      content: "PRO";
      position: absolute;
      top: 4px;
      right: 4px;
      background: var(--primary-600);
      color: white;
      font-size: 8px;
      padding: 2px 4px;
      border-radius: 3px;
      font-weight: 600;
    }

    /* Platform filter buttons */
    .platform-filter {
      display: flex;
      gap: 8px;
      margin: 16px 0;
      flex-wrap: wrap;
    }

    .filter-btn {
      padding: 6px 12px;
      border: 1px solid var(--gray-300);
      background: white;
      color: var(--gray-600);
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .filter-btn.active {
      background: var(--primary-600);
      color: white;
      border-color: var(--primary-600);
    }

    /* AI Studio Styles */
    .ai-studio-header, .templates-header, .brand-header {
      text-align: center;
      margin-bottom: 20px;
      padding: 16px;
      background: linear-gradient(135deg, var(--primary-50), var(--success-50));
      border-radius: 8px;
    }

    .studio-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 4px 0;
    }

    .studio-subtitle {
      font-size: 12px;
      color: var(--gray-600);
      margin: 0;
    }

    .quick-start-section {
      margin: 16px 0;
    }

    .quick-start-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-800);
      margin: 0 0 12px 0;
    }

    .quick-start-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }

    .quick-start-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 8px;
      border: 1px solid var(--gray-200);
      background: white;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .quick-start-btn:hover {
      background: var(--primary-50);
      border-color: var(--primary-300);
      transform: translateY(-1px);
    }

    .quick-icon {
      font-size: 20px;
      margin-bottom: 4px;
    }

    .quick-label {
      font-size: 11px;
      color: var(--gray-700);
      font-weight: 500;
    }

    .divider {
      height: 1px;
      background: var(--gray-200);
      margin: 20px 0;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .form-hint {
      font-size: 11px;
      color: var(--gray-500);
      margin-top: 4px;
      font-style: italic;
    }

    .form-textarea.enhanced, .form-select.enhanced {
      border: 2px solid var(--gray-200);
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      transition: all 0.2s;
    }

    .form-textarea.enhanced:focus, .form-select.enhanced:focus {
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      outline: none;
    }

    .ai-options {
      margin: 16px 0;
    }

    .option-group {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 8px 0;
    }

    .option-group input[type="checkbox"] {
      width: 16px;
      height: 16px;
      accent-color: var(--primary-600);
    }

    .option-group label {
      font-size: 13px;
      color: var(--gray-700);
      cursor: pointer;
    }

    .btn-ai-generate {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
      color: white;
      border: none;
      padding: 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
    }

    .btn-ai-generate:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .btn-text {
      font-weight: 600;
      font-size: 14px;
    }

    .btn-subtext {
      font-size: 11px;
      opacity: 0.9;
    }

    /* Template Cards */
    .template-categories {
      display: flex;
      gap: 8px;
      margin: 16px 0;
      flex-wrap: wrap;
    }

    .category-btn {
      padding: 6px 12px;
      border: 1px solid var(--gray-300);
      background: white;
      color: var(--gray-600);
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .category-btn.active {
      background: var(--primary-600);
      color: white;
      border-color: var(--primary-600);
    }

    .template-section {
      margin: 20px 0;
    }

    .template-section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-800);
      margin: 0 0 12px 0;
    }

    .template-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .template-card {
      border: 1px solid var(--gray-200);
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.2s;
      background: white;
    }

    .template-card:hover {
      border-color: var(--primary-300);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .template-card.premium {
      border-color: var(--primary-200);
      background: linear-gradient(135deg, var(--primary-50), white);
    }

    .template-preview {
      position: relative;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--gray-100);
    }

    .preview-placeholder {
      font-size: 12px;
      font-weight: 600;
      color: var(--gray-600);
      text-align: center;
    }

    .preview-placeholder.saas { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 8px 12px; border-radius: 4px; }
    .preview-placeholder.sale { background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 8px 12px; border-radius: 4px; }
    .preview-placeholder.tiktok { background: linear-gradient(135deg, #ff0050, #000); color: white; padding: 8px 12px; border-radius: 4px; }

    .template-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 8px;
      padding: 2px 6px;
      border-radius: 3px;
      font-weight: 600;
    }

    .template-badge.premium {
      background: var(--primary-600);
      color: white;
    }

    .template-info {
      padding: 12px;
    }

    .template-name {
      font-size: 13px;
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 4px 0;
    }

    .template-desc {
      font-size: 11px;
      color: var(--gray-600);
      margin: 0 0 8px 0;
    }

    .template-stats {
      display: flex;
      gap: 8px;
    }

    .stat {
      font-size: 10px;
      padding: 2px 6px;
      background: var(--gray-100);
      color: var(--gray-600);
      border-radius: 3px;
    }

    /* Brand Kit Styles */
    .brand-section {
      margin: 20px 0;
      padding: 16px;
      border: 1px solid var(--gray-200);
      border-radius: 8px;
      background: white;
    }

    .brand-section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-800);
      margin: 0 0 12px 0;
    }

    .color-palette {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      align-items: center;
    }

    .color-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
    }

    .color-swatch {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .color-label, .color-code {
      font-size: 10px;
      color: var(--gray-600);
    }

    .add-color-btn, .add-font-btn {
      padding: 8px 12px;
      border: 2px dashed var(--gray-300);
      background: transparent;
      color: var(--gray-500);
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .add-color-btn:hover, .add-font-btn:hover {
      border-color: var(--primary-400);
      color: var(--primary-600);
    }

    .font-stack {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .font-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid var(--gray-200);
      border-radius: 6px;
    }

    .font-preview {
      font-size: 14px;
    }

    .font-usage {
      font-size: 11px;
      color: var(--gray-500);
    }

    .btn-brand-apply {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-600), var(--success-500));
      color: white;
      border: none;
      padding: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-top: 20px;
    }

    .btn-brand-apply:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    /* Tab styling enhancements */
    .tab-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 12px 8px;
    }

    .tab-icon {
      font-size: 16px;
    }

    .tab-label {
      font-size: 10px;
      font-weight: 500;
    }

    .tab-btn.active {
      background: var(--primary-50);
      color: var(--primary-700);
    }

    /* Loading animation for AI generation */
    .generation-status {
      text-align: center;
      padding: 20px;
      background: var(--primary-50);
      border-radius: 8px;
      margin-top: 16px;
    }

    .loading-dots {
      display: flex;
      justify-content: center;
      gap: 4px;
      margin-bottom: 12px;
    }

    .loading-dots span {
      width: 8px;
      height: 8px;
      background: var(--primary-600);
      border-radius: 50%;
      animation: loading 1.4s infinite ease-in-out;
    }

    .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
    .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes loading {
      0%, 80%, 100% { transform: scale(0); }
      40% { transform: scale(1); }
    }

    .status-text {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-800);
      margin: 0 0 4px 0;
    }

    .status-subtext {
      font-size: 12px;
      color: var(--gray-600);
      margin: 0;
    }
  </style>
</head>

<body>
  <div class="builder-container">
    <%= yield %>
  </div>
</body>
</html>