<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "RapidMarkt - AI Marketing Platform" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="turbo-cache-control" content="no-cache">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-50 min-h-screen">
    <% if user_signed_in? %>
      <!-- Navigation -->
      <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex">
              <!-- Logo -->
              <div class="flex-shrink-0 flex items-center">
                <%= link_to root_path, class: "text-2xl font-bold text-indigo-600" do %>
                  RapidMarkt
                <% end %>
              </div>
              
              <!-- Navigation Links -->
              <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                <%= link_to "Dashboard", dashboard_path, 
                    class: "#{current_page?(dashboard_path) ? 'border-indigo-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium" %>
                
                <%= link_to "Campaigns", campaigns_path, 
                    class: "#{current_page?(campaigns_path) ? 'border-indigo-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium" %>
                
                <%= link_to "Contacts", contacts_path, 
                    class: "#{current_page?(contacts_path) ? 'border-indigo-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium" %>
                
                <%= link_to "Templates", templates_path, 
                    class: "#{current_page?(templates_path) ? 'border-indigo-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium" %>
                
                <%= link_to "Analytics", analytics_path, 
                    class: "#{current_page?(analytics_path) ? 'border-indigo-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium" %>
              </div>
            </div>
            
            <!-- User Menu -->
            <div class="hidden sm:ml-6 sm:flex sm:items-center">
              <div class="navbar-dropdown relative" data-controller="dropdown">
                <div>
                  <button type="button"
                          class="user-avatar"
                          data-action="click->dropdown#toggle"
                          aria-expanded="false"
                          aria-haspopup="true"
                          title="User menu">
                    <span class="sr-only">Open user menu</span>
                    <span>
                      <%= current_user.first_name&.first || current_user.email.first.upcase %>
                    </span>
                  </button>
                </div>
                
                <div class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-50"
                     data-dropdown-target="menu"
                     role="menu"
                     aria-orientation="vertical"
                     style="z-index: 9999;">
                  <div class="py-1" role="none">
                    <%= link_to "Account Settings", account_path, 
                        class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", 
                        role: "menuitem" %>
                    <%= link_to "Team", team_account_path, 
                        class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", 
                        role: "menuitem" %>
                    <%= link_to "Billing", billing_account_path, 
                        class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", 
                        role: "menuitem" %>
                    <hr class="my-1">
                    <%= link_to "Sign out", destroy_user_session_path, 
                        data: { turbo_method: :delete },
                        class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", 
                        role: "menuitem" %>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Mobile menu button -->
            <div class="-mr-2 flex items-center sm:hidden">
              <button type="button" 
                      class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" 
                      data-controller="mobile-menu"
                      data-action="click->mobile-menu#toggle"
                      aria-controls="mobile-menu" 
                      aria-expanded="false">
                <span class="sr-only">Open main menu</span>
                <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Mobile menu -->
        <div class="sm:hidden hidden" data-mobile-menu-target="menu" id="mobile-menu">
          <div class="pt-2 pb-3 space-y-1">
            <%= link_to "Dashboard", dashboard_path, 
                class: "#{current_page?(dashboard_path) ? 'bg-indigo-50 border-indigo-500 text-indigo-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium" %>
            <%= link_to "Campaigns", campaigns_path, 
                class: "#{current_page?(campaigns_path) ? 'bg-indigo-50 border-indigo-500 text-indigo-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium" %>
            <%= link_to "Contacts", contacts_path, 
                class: "#{current_page?(contacts_path) ? 'bg-indigo-50 border-indigo-500 text-indigo-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium" %>
            <%= link_to "Templates", templates_path, 
                class: "#{current_page?(templates_path) ? 'bg-indigo-50 border-indigo-500 text-indigo-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium" %>
            <%= link_to "Analytics", analytics_path, 
                class: "#{current_page?(analytics_path) ? 'bg-indigo-50 border-indigo-500 text-indigo-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium" %>
          </div>
        </div>
      </nav>
      
      <!-- Flash Messages -->
      <% if notice %>
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mx-4 mt-4" 
             data-controller="flash" 
             data-flash-auto-dismiss-value="true">
          <span class="block sm:inline"><%= notice %></span>
          <span class="absolute top-0 bottom-0 right-0 px-4 py-3" data-action="click->flash#dismiss">
            <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>Close</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      <% end %>
      
      <% if alert %>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mx-4 mt-4" 
             data-controller="flash" 
             data-flash-auto-dismiss-value="true">
          <span class="block sm:inline"><%= alert %></span>
          <span class="absolute top-0 bottom-0 right-0 px-4 py-3" data-action="click->flash#dismiss">
            <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>Close</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      <% end %>
      
      <!-- Main Content -->
      <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <%= yield %>
      </main>
    <% else %>
      <!-- Unauthenticated Layout -->
      <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
          <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
              RapidMarkt
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              AI Marketing Platform for SMEs
            </p>
          </div>
          <%= yield %>
        </div>
      </div>
    <% end %>
  </body>
</html>
