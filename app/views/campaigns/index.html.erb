<% content_for :title, "Campaigns - RapidMarkt" %>

<div class="px-4 sm:px-6 lg:px-8">
  <!-- Page header -->
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-2xl font-semibold text-gray-900">Campaigns</h1>
      <p class="mt-2 text-sm text-gray-700">
        Manage your email marketing campaigns and track their performance.
      </p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to "New Campaign", new_campaign_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto" %>
    </div>
  </div>

  <!-- Filters -->
  <div class="mt-6 bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with url: campaigns_path, method: :get, local: true, class: "space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4" do |form| %>
        <div class="flex-1">
          <%= form.label :search, "Search campaigns", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :search, value: params[:search], 
              placeholder: "Search by name or subject...",
              class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
        </div>
        
        <div>
          <%= form.label :status, "Status", class: "block text-sm font-medium text-gray-700" %>
          <%= form.select :status, 
              options_for_select([
                ['All Statuses', ''],
                ['Draft', 'draft'],
                ['Scheduled', 'scheduled'],
                ['Sending', 'sending'],
                ['Sent', 'sent']
              ], params[:status]),
              {},
              { class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" } %>
        </div>
        
        <div>
          <%= form.submit "Filter", 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
        
        <% if params[:search].present? || params[:status].present? %>
          <div>
            <%= link_to "Clear", campaigns_path, 
                class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Bulk Actions -->
  <% if @campaigns.any? %>
    <div class="mt-6 bg-white shadow rounded-lg p-4" data-controller="bulk-actions">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input type="checkbox" data-action="change->bulk-actions#toggleAll" data-bulk-actions-target="selectAll" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
            <span class="ml-2 text-sm text-gray-700">Select All</span>
          </label>
          <span class="text-sm text-gray-500" data-bulk-actions-target="selectedCount">0 selected</span>
        </div>
        <div class="flex items-center space-x-2" data-bulk-actions-target="actions" style="display: none;">
          <%= button_to "Send Selected", bulk_send_campaigns_path, 
              params: { campaign_ids: [] },
              data: { 
                turbo_method: :post, 
                turbo_confirm: "Are you sure you want to send the selected campaigns? This action cannot be undone.",
                bulk_actions_target: "sendButton"
              },
              class: "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <%= button_to "Schedule Selected", bulk_schedule_campaigns_path,
              params: { campaign_ids: [] },
              data: { 
                turbo_method: :post,
                bulk_actions_target: "scheduleButton"
              },
              class: "inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Campaigns list -->
  <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-md">
    <% if @campaigns.any? %>
      <ul role="list" class="divide-y divide-gray-200">
        <% @campaigns.each do |campaign| %>
          <li data-controller="dropdown">
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex items-center min-w-0 flex-1">
                  <div class="flex-shrink-0 mr-4">
                    <input type="checkbox" 
                           data-action="change->bulk-actions#updateSelection" 
                           data-bulk-actions-target="campaignCheckbox" 
                           data-campaign-id="<%= campaign.id %>"
                           data-campaign-status="<%= campaign.status %>"
                           class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                  </div>
                  <div class="flex-shrink-0">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                      <%= case campaign.status
                          when 'draft' then 'bg-gray-100 text-gray-800'
                          when 'sending' then 'bg-yellow-100 text-yellow-800'
                          when 'sent' then 'bg-green-100 text-green-800'
                          when 'scheduled' then 'bg-blue-100 text-blue-800'
                          else 'bg-gray-100 text-gray-800'
                          end %>">
                      <%= campaign.status.humanize %>
                    </span>
                  </div>
                  <div class="ml-4 min-w-0 flex-1">
                    <div class="flex items-center">
                      <p class="text-sm font-medium text-indigo-600 truncate">
                        <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-indigo-900" %>
                      </p>
                    </div>
                    <div class="mt-1">
                      <p class="text-sm text-gray-900 truncate">
                        Subject: <%= campaign.subject %>
                      </p>
                      <div class="mt-1 flex items-center text-sm text-gray-500">
                        <p>
                          Created <%= time_ago_in_words(campaign.created_at) %> ago
                        </p>
                        <% if campaign.scheduled_at.present? %>
                          <span class="mx-2">•</span>
                          <p>
                            Scheduled for <%= campaign.scheduled_at.strftime("%B %d, %Y at %I:%M %p") %>
                          </p>
                        <% end %>
                        <% if campaign.sent_at.present? %>
                          <span class="mx-2">•</span>
                          <p>
                            Sent <%= time_ago_in_words(campaign.sent_at) %> ago
                          </p>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center space-x-4">
                  <% if campaign.sent? %>
                    <div class="text-right">
                      <p class="text-sm text-gray-900">
                        <%= campaign.campaign_contacts.where.not(sent_at: nil).count %> sent
                      </p>
                      <p class="text-sm text-gray-500">
                        <%= campaign.campaign_contacts.where.not(opened_at: nil).count %> opened
                      </p>
                    </div>
                  <% end %>
                  
                  <!-- Actions dropdown -->
                  <div class="relative">
                    <button type="button" 
                            data-action="click->dropdown#toggle"
                            class="inline-flex items-center p-2 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                      <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </button>
                    
                    <div data-dropdown-target="menu" 
                         class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <div class="py-1">
                        <%= link_to "View", campaign_path(campaign), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                        <%= link_to "Edit", edit_campaign_path(campaign), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                        <%= link_to "Preview", preview_campaign_path(campaign), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                            target: "_blank" %>
                        <% if campaign.draft? %>
                          <%= link_to "Send Test", send_test_campaign_path(campaign), 
                              data: { turbo_method: :post },
                              class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                        <% end %>
                        <% unless campaign.sent? %>
                          <%= link_to "Delete", campaign_path(campaign), 
                              data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to delete this campaign?" },
                              class: "block px-4 py-2 text-sm text-red-700 hover:bg-red-100" %>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
      
      <!-- Pagination -->
      <% if @campaigns.respond_to?(:total_pages) %>
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <% if @campaigns.prev_page %>
                <%= link_to "Previous", campaigns_path(page: @campaigns.prev_page, search: params[:search], status: params[:status]), 
                    class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
              <% end %>
              <% if @campaigns.next_page %>
                <%= link_to "Next", campaigns_path(page: @campaigns.next_page, search: params[:search], status: params[:status]), 
                    class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
              <% end %>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing
                  <span class="font-medium"><%= (@campaigns.current_page - 1) * @campaigns.limit_value + 1 %></span>
                  to
                  <span class="font-medium"><%= [@campaigns.current_page * @campaigns.limit_value, @campaigns.total_count].min %></span>
                  of
                  <span class="font-medium"><%= @campaigns.total_count %></span>
                  results
                </p>
              </div>
              <div>
                <%= paginate @campaigns if defined?(Kaminari) %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">
          <% if params[:search].present? || params[:status].present? %>
            No campaigns found
          <% else %>
            No campaigns
          <% end %>
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          <% if params[:search].present? || params[:status].present? %>
            Try adjusting your search or filter criteria.
          <% else %>
            Get started by creating your first email campaign.
          <% end %>
        </p>
        <div class="mt-6">
          <% if params[:search].present? || params[:status].present? %>
            <%= link_to "Clear filters", campaigns_path, 
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <% else %>
            <%= link_to "New Campaign", new_campaign_path, 
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>