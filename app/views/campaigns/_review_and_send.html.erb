<!-- Review and Send Interface -->
<div class="space-y-8">
  <div class="text-center mb-8">
    <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center">
      <svg class="w-8 h-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    </div>
    <h3 class="text-2xl font-bold text-gray-900 mb-2">Review & Send Campaign</h3>
    <p class="text-gray-600">Review all settings and send your campaign to your audience</p>
  </div>

  <!-- Campaign Summary Cards -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Campaign Details Card -->
    <div class="bg-white rounded-2xl shadow-lg border-0 overflow-hidden">
      <div class="bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4 border-b border-gray-100">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900">Campaign Details</h4>
            <p class="text-sm text-gray-600">Basic campaign information</p>
          </div>
        </div>
      </div>
      
      <div class="p-6 space-y-4">
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Campaign Name:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-campaign-name">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Subject Line:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-subject">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">From:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-from">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Reply-To:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-reply-to">Same as sender</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Preview Text:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-preview-text">None</span>
        </div>
        
        <div class="pt-4 border-t border-gray-100">
          <button type="button" 
                  class="text-sm text-indigo-600 hover:text-indigo-800 font-medium transition-colors duration-200"
                  data-action="click->campaign-wizard#goToStep" 
                  data-step="1">
            Edit Details →
          </button>
        </div>
      </div>
    </div>

    <!-- Audience Card -->
    <div class="bg-white rounded-2xl shadow-lg border-0 overflow-hidden">
      <div class="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-100">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900">Target Audience</h4>
            <p class="text-sm text-gray-600">Who will receive this campaign</p>
          </div>
        </div>
      </div>
      
      <div class="p-6 space-y-4">
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Audience Type:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-audience-type">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Total Recipients:</span>
          <span class="text-sm font-semibold text-green-600 text-right" id="review-recipient-count">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Selected Tags:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-tags">None</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Estimated Delivery:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-delivery-time">~5 minutes</span>
        </div>
        
        <div class="pt-4 border-t border-gray-100">
          <button type="button" 
                  class="text-sm text-purple-600 hover:text-purple-800 font-medium transition-colors duration-200"
                  data-action="click->campaign-wizard#goToStep" 
                  data-step="2">
            Edit Audience →
          </button>
        </div>
      </div>
    </div>

    <!-- Schedule Card -->
    <div class="bg-white rounded-2xl shadow-lg border-0 overflow-hidden">
      <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-100">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900">Schedule & Timing</h4>
            <p class="text-sm text-gray-600">When your campaign will be sent</p>
          </div>
        </div>
      </div>
      
      <div class="p-6 space-y-4">
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Send Type:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-send-type">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Send Time:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-send-time">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Timezone:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-timezone">-</span>
        </div>
        
        <div class="flex justify-between items-start">
          <span class="text-sm font-medium text-gray-500">Tracking:</span>
          <span class="text-sm font-semibold text-gray-900 text-right" id="review-tracking">Opens & Clicks</span>
        </div>
        
        <div class="pt-4 border-t border-gray-100">
          <button type="button" 
                  class="text-sm text-green-600 hover:text-green-800 font-medium transition-colors duration-200"
                  data-action="click->campaign-wizard#goToStep" 
                  data-step="4">
            Edit Schedule →
          </button>
        </div>
      </div>
    </div>

    <!-- Content Preview Card -->
    <div class="bg-white rounded-2xl shadow-lg border-0 overflow-hidden">
      <div class="bg-gradient-to-r from-orange-50 to-red-50 px-6 py-4 border-b border-gray-100">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900">Content Preview</h4>
            <p class="text-sm text-gray-600">How your email will look</p>
          </div>
        </div>
      </div>
      
      <div class="p-6">
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <p class="text-sm text-gray-600 mb-4">Email content preview</p>
          <button type="button" 
                  class="inline-flex items-center px-4 py-2 border border-orange-300 rounded-lg shadow-sm text-sm font-medium text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
                  data-action="click->campaign-wizard#showPreview">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            Preview Email
          </button>
        </div>
        
        <div class="pt-4 border-t border-gray-100 mt-6">
          <button type="button" 
                  class="text-sm text-orange-600 hover:text-orange-800 font-medium transition-colors duration-200"
                  data-action="click->campaign-wizard#goToStep" 
                  data-step="3">
            Edit Content →
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Multi-Device Preview Carousel -->
  <div class="bg-white rounded-2xl shadow-lg border-0 overflow-hidden" data-controller="preview-carousel">
    <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-100">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900">Device Preview</h4>
            <p class="text-sm text-gray-600">See how your email looks on different devices</p>
          </div>
        </div>
        
        <!-- Device Toggle Buttons -->
        <div class="flex items-center bg-white rounded-lg p-1 shadow-sm">
          <button type="button" 
                  class="px-3 py-2 text-sm font-medium rounded-md bg-gray-600 text-white transition-all duration-200" 
                  data-preview-carousel-target="deviceToggle" 
                  data-device="desktop"
                  data-action="click->preview-carousel#switchDevice">
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </button>
          <button type="button" 
                  class="px-3 py-2 text-sm font-medium rounded-md bg-gray-200 text-gray-700 transition-all duration-200" 
                  data-preview-carousel-target="deviceToggle" 
                  data-device="tablet"
                  data-action="click->preview-carousel#switchDevice">
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
            </svg>
          </button>
          <button type="button" 
                  class="px-3 py-2 text-sm font-medium rounded-md bg-gray-200 text-gray-700 transition-all duration-200" 
                  data-preview-carousel-target="deviceToggle" 
                  data-device="mobile"
                  data-action="click->preview-carousel#switchDevice">
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <div class="p-6">
      <div class="flex justify-center">
        <div class="preview-container transition-all duration-300" data-preview-carousel-target="container">
          <!-- Desktop Preview -->
          <div class="preview-frame desktop-frame bg-gray-100 rounded-lg p-4 shadow-inner" 
               data-preview-carousel-target="frame" 
               data-device="desktop"
               style="width: 100%; max-width: 800px;">
            <div class="bg-white rounded border shadow-sm min-h-96 p-6">
              <div class="text-center text-gray-500">
                <p class="text-sm">Desktop email preview will appear here</p>
              </div>
            </div>
          </div>
          
          <!-- Tablet Preview -->
          <div class="preview-frame tablet-frame bg-gray-100 rounded-lg p-4 shadow-inner hidden" 
               data-preview-carousel-target="frame" 
               data-device="tablet"
               style="width: 768px; max-width: 100%;">
            <div class="bg-white rounded border shadow-sm min-h-96 p-4">
              <div class="text-center text-gray-500">
                <p class="text-sm">Tablet email preview will appear here</p>
              </div>
            </div>
          </div>
          
          <!-- Mobile Preview -->
          <div class="preview-frame mobile-frame bg-gray-100 rounded-lg p-4 shadow-inner hidden" 
               data-preview-carousel-target="frame" 
               data-device="mobile"
               style="width: 375px; max-width: 100%;">
            <div class="bg-white rounded border shadow-sm min-h-96 p-3">
              <div class="text-center text-gray-500">
                <p class="text-xs">Mobile email preview will appear here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Email Section -->
  <div class="bg-white rounded-2xl shadow-lg border-0 overflow-hidden" data-controller="test-email">
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
      <div class="flex items-center">
        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
          <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <div>
          <h4 class="text-lg font-semibold text-gray-900">Send Test Email</h4>
          <p class="text-sm text-gray-600">Test your campaign before sending to your audience</p>
        </div>
      </div>
    </div>

    <div class="p-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-semibold text-gray-700 mb-3">Test Email Recipients</label>
          <div class="space-y-3">
            <div class="flex">
              <input type="email"
                     placeholder="Enter email address"
                     class="flex-1 px-4 py-3 border-gray-300 rounded-l-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                     data-test-email-target="emailInput">
              <button type="button"
                      class="px-4 py-3 bg-blue-600 text-white rounded-r-xl hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      data-action="click->test-email#addEmail">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>

            <!-- Email List -->
            <div class="space-y-2" data-test-email-target="emailList">
              <!-- Added emails will appear here -->
            </div>

            <!-- Quick Add Buttons -->
            <div class="flex flex-wrap gap-2">
              <button type="button"
                      class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-full text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
                      data-action="click->test-email#addCurrentUser">
                <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Add Me
              </button>
              <button type="button"
                      class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-full text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
                      data-action="click->test-email#addTeamEmails">
                <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Add Team
              </button>
            </div>
          </div>
        </div>

        <div>
          <label class="block text-sm font-semibold text-gray-700 mb-3">Test Options</label>
          <div class="space-y-4">
            <label class="flex items-start">
              <input type="checkbox"
                     checked
                     class="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                     data-test-email-target="includeTracking">
              <div class="ml-3">
                <span class="text-sm font-medium text-gray-700">Include tracking</span>
                <p class="text-xs text-gray-500">Test email tracking functionality</p>
              </div>
            </label>

            <label class="flex items-start">
              <input type="checkbox"
                     class="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                     data-test-email-target="useTestData">
              <div class="ml-3">
                <span class="text-sm font-medium text-gray-700">Use test data</span>
                <p class="text-xs text-gray-500">Replace merge fields with sample data</p>
              </div>
            </label>

            <div class="pt-4">
              <button type="button"
                      class="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105"
                      data-action="click->test-email#sendTest"
                      data-test-email-target="sendButton">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                Send Test Email
              </button>
            </div>

            <!-- Test Status -->
            <div class="test-status hidden" data-test-email-target="status">
              <!-- Status messages will appear here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Final Send Confirmation -->
  <div class="bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-2xl border-2 border-green-200 p-8">
    <div class="text-center">
      <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center">
        <svg class="w-10 h-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
        </svg>
      </div>

      <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Send Your Campaign?</h3>
      <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
        Your campaign is configured and ready to go. Once sent, you'll be able to monitor its performance in real-time.
      </p>

      <!-- Final Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-green-600" id="final-recipient-count">-</div>
          <div class="text-sm text-gray-600">Recipients</div>
        </div>
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-blue-600" id="final-delivery-time">~5 min</div>
          <div class="text-sm text-gray-600">Est. Delivery</div>
        </div>
        <div class="bg-white rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-purple-600" id="final-send-time">Now</div>
          <div class="text-sm text-gray-600">Send Time</div>
        </div>
      </div>

      <!-- Send Buttons -->
      <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
        <button type="button"
                class="w-full sm:w-auto px-8 py-4 border border-gray-300 rounded-xl shadow-sm text-lg font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
          Save as Draft
        </button>

        <button type="button"
                class="w-full sm:w-auto px-8 py-4 border border-transparent rounded-xl shadow-lg text-lg font-bold text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105"
                data-action="click->campaign-wizard#showSendConfirmation">
          <svg class="-ml-1 mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
          Send Campaign Now
        </button>
      </div>

      <p class="text-xs text-gray-500 mt-4">
        By sending this campaign, you confirm that it complies with all applicable laws and regulations.
      </p>
    </div>
  </div>
</div>

<!-- Send Confirmation Modal -->
<div class="send-confirmation-modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden"
     data-campaign-wizard-target="sendModal">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-2xl bg-white">
    <div class="text-center">
      <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-red-100 to-orange-100 rounded-full flex items-center justify-center">
        <svg class="w-8 h-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>

      <h3 class="text-2xl font-bold text-gray-900 mb-4">Confirm Campaign Send</h3>
      <p class="text-gray-600 mb-6">
        Are you sure you want to send this campaign? This action cannot be undone.
      </p>

      <!-- Final Summary -->
      <div class="bg-gray-50 rounded-xl p-4 mb-6 text-left">
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="font-medium text-gray-700">Campaign:</span>
            <span class="text-gray-900" id="modal-campaign-name">-</span>
          </div>
          <div>
            <span class="font-medium text-gray-700">Recipients:</span>
            <span class="text-gray-900" id="modal-recipient-count">-</span>
          </div>
          <div>
            <span class="font-medium text-gray-700">Subject:</span>
            <span class="text-gray-900" id="modal-subject">-</span>
          </div>
          <div>
            <span class="font-medium text-gray-700">Send Time:</span>
            <span class="text-gray-900" id="modal-send-time">-</span>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-center space-x-4">
        <button type="button"
                class="px-6 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                data-action="click->campaign-wizard#hideSendConfirmation">
          Cancel
        </button>

        <button type="submit"
                class="px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-bold text-white bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                data-action="click->campaign-wizard#confirmSend">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
          Yes, Send Campaign
        </button>
      </div>
    </div>
  </div>
</div>
