<% content_for :title, "Edit Campaign - RapidMarkt" %>

<div class="px-4 sm:px-6 lg:px-8">
  <!-- Page header -->
  <div class="mb-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4">
        <li>
          <div>
            <%= link_to campaigns_path, class: "text-gray-400 hover:text-gray-500" do %>
              <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
              </svg>
              <span class="sr-only">Back</span>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to "Campaigns", campaigns_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to @campaign.name, campaign_path(@campaign), class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-4 text-sm font-medium text-gray-500">Edit</span>
          </div>
        </li>
      </ol>
    </nav>
    
    <div class="mt-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Edit Campaign</h1>
          <p class="mt-2 text-sm text-gray-700">
            Update your campaign details and content.
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
            <%= case @campaign.status
                when 'draft' then 'bg-gray-100 text-gray-800'
                when 'sending' then 'bg-yellow-100 text-yellow-800'
                when 'sent' then 'bg-green-100 text-green-800'
                when 'scheduled' then 'bg-blue-100 text-blue-800'
                else 'bg-gray-100 text-gray-800'
                end %>">
            <%= @campaign.status.humanize %>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Warning for sent campaigns -->
  <% if @campaign.sent? %>
    <div class="mb-6 rounded-md bg-yellow-50 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">
            Campaign Already Sent
          </h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>
              This campaign has already been sent and cannot be modified. You can view the campaign details or create a new campaign based on this one.
            </p>
          </div>
          <div class="mt-4">
            <div class="flex space-x-3">
              <%= link_to "View Campaign", campaign_path(@campaign), 
                  class: "text-sm bg-yellow-50 text-yellow-800 hover:bg-yellow-100 font-medium" %>
              <%= link_to "Duplicate Campaign", new_campaign_path(duplicate: @campaign.id), 
                  class: "text-sm bg-yellow-50 text-yellow-800 hover:bg-yellow-100 font-medium" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Campaign form -->
  <% unless @campaign.sent? %>
    <%= render 'form', campaign: @campaign %>
  <% end %>
</div>