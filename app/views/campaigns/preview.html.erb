<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Campaign Preview: <%= @campaign.subject %></title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f8f9fa;
    }
    .email-container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .email-header {
      background-color: #4f46e5;
      color: white;
      padding: 20px;
      text-align: center;
    }
    .email-header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
    .email-content {
      padding: 30px;
    }
    .preview-notice {
      background-color: #fef3c7;
      border: 1px solid #f59e0b;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 20px;
      font-size: 14px;
      color: #92400e;
    }
    .personalization {
      background-color: #f0f9ff;
      border: 1px solid #0ea5e9;
      border-radius: 6px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .personalization h3 {
      margin: 0 0 10px 0;
      color: #0c4a6e;
      font-size: 16px;
    }
    .personalization p {
      margin: 5px 0;
      font-size: 14px;
      color: #0369a1;
    }
    .email-footer {
      background-color: #f8f9fa;
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #6b7280;
      border-top: 1px solid #e5e7eb;
    }
    .btn {
      display: inline-block;
      padding: 12px 24px;
      background-color: #4f46e5;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      margin: 10px 0;
    }
    .btn:hover {
      background-color: #4338ca;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <h1><%= @campaign.subject %></h1>
    </div>
    
    <div class="email-content">
      <div class="preview-notice">
        <strong>📧 Campaign Preview</strong> - This is how your email will appear to recipients
      </div>
      
      <div class="personalization">
        <h3>🎯 Personalization Preview</h3>
        <p><strong>Recipient:</strong> <%= @contact.first_name %> <%= @contact.last_name %></p>
        <p><strong>Email:</strong> <%= @contact.email %></p>
        <p><strong>Campaign:</strong> <%= @campaign.name %></p>
        <% if @campaign.from_name.present? %>
          <p><strong>From:</strong> <%= @campaign.from_name %> &lt;<%= @campaign.from_email || '<EMAIL>' %>&gt;</p>
        <% end %>
      </div>
      
      <div class="email-body">
        <% if @campaign.template&.body.present? %>
          <%= simple_format(@campaign.template.body) %>
        <% else %>
          <p>Hello <%= @contact.first_name %>,</p>
          
          <p>This is a preview of your campaign content. Your actual email content will appear here once you've added it to the campaign.</p>
          
          <p>You can personalize your emails using variables like:</p>
          <ul>
            <li>{{first_name}} - Recipient's first name</li>
            <li>{{last_name}} - Recipient's last name</li>
            <li>{{email}} - Recipient's email address</li>
            <li>{{company}} - Recipient's company (if available)</li>
          </ul>
          
          <p>Best regards,<br>
          Your Team</p>
        <% end %>
      </div>
      

    </div>
    
    <div class="email-footer">
      <p>This email was sent as part of the "<%= @campaign.name %>" campaign.</p>
      <p>You are receiving this because you subscribed to our mailing list.</p>
      <p><a href="#" style="color: #6b7280;">Unsubscribe</a> | <a href="#" style="color: #6b7280;">Update Preferences</a></p>
    </div>
  </div>
  
  <div style="text-align: center; margin-top: 20px; padding: 20px;">
    <p style="color: #6b7280; font-size: 14px; margin-bottom: 15px;">
      📊 Campaign Status: <strong style="color: #059669;"><%= @campaign.status.titleize %></strong>
    </p>
    <% if @campaign.draft? %>
      <a href="<%= edit_campaign_path(@campaign) %>" class="btn">✏️ Edit Campaign</a>
    <% end %>
  </div>
</body>
</html>