<% content_for :title, "Contacts - RapidMarkt" %>

<div class="px-4 sm:px-6 lg:px-8">
  <!-- Page header -->
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-2xl font-semibold text-gray-900">Contacts</h1>
      <p class="mt-2 text-sm text-gray-700">
        Manage your email subscribers and organize them with tags.
      </p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
      <%= link_to "Import Contacts", import_contacts_path, 
          class: "inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto" %>
      <%= link_to "Add Contact", new_contact_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto" %>
    </div>
  </div>

  <!-- Filters -->
  <div class="mt-6 bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with url: contacts_path, method: :get, local: true, class: "space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4" do |form| %>
        <div class="flex-1">
          <%= form.label :search, "Search contacts", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :search, value: params[:search], 
              placeholder: "Search by name or email...",
              class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
        </div>
        
        <div>
          <%= form.label :status, "Status", class: "block text-sm font-medium text-gray-700" %>
          <%= form.select :status, 
              options_for_select([
                ['All Statuses', ''],
                ['Subscribed', 'subscribed'],
                ['Unsubscribed', 'unsubscribed']
              ], params[:status]),
              {},
              { class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" } %>
        </div>
        
        <div>
          <%= form.label :tag, "Tag", class: "block text-sm font-medium text-gray-700" %>
          <%= form.collection_select :tag, 
              @current_account.tags.order(:name), 
              :id, :name, 
              { prompt: "All Tags" },
              { class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" } %>
        </div>
        
        <div>
          <%= form.submit "Filter", 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
        
        <% if params[:search].present? || params[:status].present? || params[:tag].present? %>
          <div>
            <%= link_to "Clear", contacts_path, 
                class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Bulk Actions -->
  <% if @contacts.any? %>
    <div class="mt-6 bg-white shadow rounded-lg">
      <div class="px-4 py-3 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input type="checkbox" id="select-all" 
                   class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
            <label for="select-all" class="ml-3 text-sm font-medium text-gray-700">Select all</label>
          </div>
          <div class="flex items-center space-x-3">
            <button type="button" id="bulk-tag" disabled
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
              Add Tag
            </button>
            <button type="button" id="bulk-unsubscribe" disabled
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
              Unsubscribe
            </button>
            <button type="button" id="bulk-delete" disabled
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50">
              Delete
            </button>
            <%= link_to "Export", export_contacts_path(format: :csv, search: params[:search], status: params[:status], tag: params[:tag]), 
                class: "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Contacts list -->
  <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-md">
    <% if @contacts.any? %>
      <ul role="list" class="divide-y divide-gray-200">
          <% @contacts.each do |contact| %>
            <li>
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex items-center min-w-0 flex-1">
                  <div class="flex-shrink-0">
                    <input type="checkbox" class="contact-checkbox focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" 
                           value="<%= contact.id %>">
                  </div>
                  <div class="ml-4 min-w-0 flex-1">
                    <div class="flex items-center">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        <%= contact.first_name %> <%= contact.last_name %>
                      </p>
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        <%= contact.subscribed? ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= contact.status.humanize %>
                      </span>
                    </div>
                    <div class="mt-1">
                      <p class="text-sm text-gray-500 truncate">
                        <%= contact.email %>
                      </p>
                      <div class="mt-1 flex items-center text-sm text-gray-500">
                        <p>
                          Added <%= time_ago_in_words(contact.created_at) %> ago
                        </p>
                        <% if contact.last_opened_at.present? %>
                          <span class="mx-2">•</span>
                          <p>
                            Last opened <%= time_ago_in_words(contact.last_opened_at) %> ago
                          </p>
                        <% end %>
                      </div>
                      <% if contact.tags.any? %>
                        <div class="mt-2 flex flex-wrap gap-1">
                          <% contact.tags.each do |tag| %>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <%= tag.name %>
                            </span>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center space-x-4">
                  <div class="text-right">
                    <p class="text-sm text-gray-900">
                      <%= contact.campaign_contacts.where.not(sent_at: nil).count %> emails received
                    </p>
                    <p class="text-sm text-gray-500">
                      <%= contact.campaign_contacts.where.not(opened_at: nil).count %> opened
                    </p>
                  </div>
                  
                  <!-- Modern Actions Button -->
                  <div class="relative">
                    <button type="button" 
                            data-contact-id="<%= contact.id %>"
                            data-contact-name="<%= contact.full_name %>"
                            data-contact-email="<%= contact.email %>"
                            data-contact-status="<%= contact.status %>"
                            data-contact-tags='<%= contact.tags.pluck(:name).to_json %>'
                            data-action="click->contact-modal#open"
                            class="group inline-flex items-center p-2.5 border border-transparent rounded-xl shadow-sm text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105">
                      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                      </svg>
                      <span class="ml-2 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200">Actions</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
      
      <!-- Pagination -->
      <% if @contacts.respond_to?(:total_pages) %>
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <% if @contacts.prev_page %>
                <%= link_to "Previous", contacts_path(page: @contacts.prev_page, search: params[:search], status: params[:status], tag: params[:tag]), 
                    class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
              <% end %>
              <% if @contacts.next_page %>
                <%= link_to "Next", contacts_path(page: @contacts.next_page, search: params[:search], status: params[:status], tag: params[:tag]), 
                    class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
              <% end %>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing
                  <span class="font-medium"><%= (@contacts.current_page - 1) * @contacts.limit_value + 1 %></span>
                  to
                  <span class="font-medium"><%= [@contacts.current_page * @contacts.limit_value, @contacts.total_count].min %></span>
                  of
                  <span class="font-medium"><%= @contacts.total_count %></span>
                  results
                </p>
              </div>
              <div>
                <%= paginate @contacts if defined?(Kaminari) %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">
          <% if params[:search].present? || params[:status].present? || params[:tag].present? %>
            No contacts found
          <% else %>
            No contacts
          <% end %>
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          <% if params[:search].present? || params[:status].present? || params[:tag].present? %>
            Try adjusting your search or filter criteria.
          <% else %>
            Get started by adding your first contact or importing a list.
          <% end %>
        </p>
        <div class="mt-6 flex justify-center space-x-3">
          <% if params[:search].present? || params[:status].present? || params[:tag].present? %>
            <%= link_to "Clear filters", contacts_path, 
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <% else %>
            <%= link_to "Add Contact", new_contact_path, 
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <%= link_to "Import Contacts", import_contacts_path, 
                class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Contact Actions Modal -->
  <div data-contact-modal-target="modal" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div data-contact-modal-target="backdrop" 
         data-action="click->contact-modal#backdropClick"
         class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0 bg-gray-500 bg-opacity-75 transition-opacity opacity-0">
      
      <!-- This element is to trick the browser into centering the modal contents. -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      
      <div data-contact-modal-target="content"
           class="inline-block align-bottom bg-white rounded-2xl px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6 opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        
        <!-- Modal Header -->
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
              Contact Actions
            </h3>
            <div class="mt-2">
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <h4 data-contact-modal-target="contactName" class="text-sm font-semibold text-gray-900"></h4>
                  <span data-contact-modal-target="contactStatus" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"></span>
                </div>
                <p data-contact-modal-target="contactEmail" class="text-sm text-gray-600"></p>
                <div class="flex flex-wrap gap-1" data-contact-modal-target="contactTags"></div>
              </div>
            </div>
          </div>
          <div class="absolute top-0 right-0 pt-4 pr-4">
            <button type="button" 
                    data-action="click->contact-modal#close"
                    class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Modal Actions -->
        <div class="mt-6 grid grid-cols-1 gap-3 sm:grid-cols-2">
          <!-- View Action -->
           <button type="button" 
                    data-action="click->contact-modal#handleView"
                    class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              View Details
            </button>
          
          <!-- Edit Action -->
           <button type="button" 
                   data-action="click->contact-modal#handleEdit"
                   class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
             <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
             </svg>
             Edit Contact
           </button>
          
          <!-- Subscribe/Unsubscribe Action -->
          <button type="button" 
                  data-action="click->contact-modal#handleSubscriptionToggle"
                  data-action="subscribe"
                  class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <!-- Icon will be updated by JavaScript -->
            </svg>
            <!-- Text will be updated by JavaScript -->
          </button>
          
          <!-- Delete Action -->
          <button type="button" 
                  data-action="click->contact-modal#handleDelete"
                  data-action="delete"
                  class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete Contact
          </button>
        </div>
        
        <!-- Modal Footer -->
        <div class="mt-6 flex justify-end">
          <button type="button" 
                  data-action="click->contact-modal#close"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox');
    const bulkButtons = document.querySelectorAll('#bulk-tag, #bulk-unsubscribe, #bulk-delete');
    
    function updateBulkButtons() {
      const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
      const hasSelection = checkedBoxes.length > 0;
      
      bulkButtons.forEach(button => {
        button.disabled = !hasSelection;
      });
    }
    
    selectAllCheckbox?.addEventListener('change', function() {
      contactCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
      updateBulkButtons();
    });
    
    contactCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const allChecked = Array.from(contactCheckboxes).every(cb => cb.checked);
        const noneChecked = Array.from(contactCheckboxes).every(cb => !cb.checked);
        
        if (selectAllCheckbox) {
          selectAllCheckbox.checked = allChecked;
          selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
        }
        
        updateBulkButtons();
      });
    });
    
    // Initialize bulk button states
    updateBulkButtons();
  });
</script>