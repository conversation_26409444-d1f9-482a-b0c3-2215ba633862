<%# Email Automations Dashboard %>
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <%# Header Section %>
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Email Automations</h1>
          <p class="text-gray-600">Create and manage automated email sequences to engage your audience</p>
        </div>
        
        <%# Create New Automation Button %>
        <%= link_to new_automation_path, 
            class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Create Automation
        <% end %>
      </div>
    </div>

    <%# Search and Filter Section %>
    <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border-0">
      <div class="flex flex-col lg:flex-row gap-4">
        <%# Search Bar %>
        <div class="flex-1">
          <%= form_with url: automations_path, method: :get, local: true, class: "flex" do |form| %>
            <div class="relative flex-1">
              <%= form.text_field :search, 
                  value: params[:search], 
                  placeholder: "Search automations...", 
                  class: "w-full pl-10 pr-4 py-3 bg-white rounded-2xl border-0 shadow-sm focus:ring-2 focus:ring-indigo-500 focus:outline-none transition-all duration-200" %>
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>
          <% end %>
        </div>

        <%# Status Filter %>
        <div class="flex gap-2">
          <% %w[all draft active paused archived].each do |status| %>
            <%= link_to automations_path(status: status == 'all' ? nil : status), 
                class: "px-4 py-2 rounded-xl font-medium transition-all duration-200 #{params[:status] == status || (status == 'all' && params[:status].blank?) ? 'bg-indigo-600 text-white shadow-lg' : 'bg-white text-gray-600 hover:bg-gray-50 shadow-sm'}" do %>
              <%= status.humanize %>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <%# Bulk Actions Bar (hidden by default) %>
    <div id="bulk-actions-bar" class="hidden bg-indigo-600 rounded-2xl p-4 mb-6 shadow-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center text-white">
          <span id="selected-count" class="font-semibold mr-4">0 selected</span>
        </div>
        <div class="flex gap-2">
          <button type="button" id="bulk-activate" class="px-4 py-2 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors">
            Activate
          </button>
          <button type="button" id="bulk-pause" class="px-4 py-2 bg-yellow-500 text-white rounded-xl hover:bg-yellow-600 transition-colors">
            Pause
          </button>
          <button type="button" id="bulk-delete" class="px-4 py-2 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors">
            Delete
          </button>
          <button type="button" id="clear-selection" class="px-4 py-2 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
            Clear
          </button>
        </div>
      </div>
    </div>

    <%# Main Content %>
    <% if @automations.any? %>
      <%# Automations Grid %>
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <% @automations.each do |automation| %>
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0">
            <%# Automation Header %>
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center">
                <input type="checkbox" class="automation-checkbox mr-3 rounded focus:ring-indigo-500" 
                       data-automation-id="<%= automation.id %>">
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-1">
                    <%= link_to automation.name, automation_path(automation), 
                        class: "hover:text-indigo-600 transition-colors" %>
                  </h3>
                  <p class="text-sm text-gray-600 line-clamp-2"><%= automation.description %></p>
                </div>
              </div>
              
              <%# Status Badge %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                <%= case automation.status
                    when 'active' then 'bg-green-100 text-green-800'
                    when 'paused' then 'bg-yellow-100 text-yellow-800'
                    when 'draft' then 'bg-gray-100 text-gray-800'
                    when 'archived' then 'bg-red-100 text-red-800'
                    else 'bg-gray-100 text-gray-800'
                    end %>">
                <%= automation.status.humanize %>
              </span>
            </div>

            <%# Trigger Type %>
            <div class="mb-4">
              <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-indigo-100 text-indigo-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                </svg>
                <%= automation.trigger_type.humanize %>
              </span>
            </div>

            <%# Stats %>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900"><%= automation.total_enrollments %></div>
                <div class="text-xs text-gray-600">Total Enrolled</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900"><%= automation.completion_rate %>%</div>
                <div class="text-xs text-gray-600">Completion Rate</div>
              </div>
            </div>

            <%# Last Activity %>
            <div class="text-xs text-gray-500 mb-4">
              Last updated <%= time_ago_in_words(automation.updated_at) %> ago
            </div>

            <%# Action Buttons %>
            <div class="flex gap-2">
              <% if automation.can_be_activated? %>
                <%= link_to activate_automation_path(automation), method: :patch,
                    class: "flex-1 px-3 py-2 bg-green-500 text-white text-sm font-medium rounded-xl hover:bg-green-600 transition-colors text-center",
                    data: { confirm: "Activate this automation?" } do %>
                  Activate
                <% end %>
              <% elsif automation.can_be_paused? %>
                <%= link_to pause_automation_path(automation), method: :patch,
                    class: "flex-1 px-3 py-2 bg-yellow-500 text-white text-sm font-medium rounded-xl hover:bg-yellow-600 transition-colors text-center",
                    data: { confirm: "Pause this automation?" } do %>
                  Pause
                <% end %>
              <% end %>
              
              <%= link_to duplicate_automation_path(automation), method: :post,
                  class: "flex-1 px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded-xl hover:bg-blue-600 transition-colors text-center" do %>
                Duplicate
              <% end %>
              
              <%# Dropdown Menu %>
              <div class="relative" data-controller="dropdown">
                <button type="button" data-action="click->dropdown#toggle" 
                        class="px-3 py-2 bg-gray-100 text-gray-600 rounded-xl hover:bg-gray-200 transition-colors">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                  </svg>
                </button>
                
                <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg z-10 border-0">
                  <%= link_to automation_path(automation), 
                      class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-t-xl" do %>
                    View Details
                  <% end %>
                  <%= link_to edit_automation_path(automation), 
                      class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50" do %>
                    Edit
                  <% end %>
                  <%= link_to analytics_automation_path(automation), 
                      class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50" do %>
                    Analytics
                  <% end %>
                  <hr class="my-1">
                  <%= link_to automation_path(automation), method: :delete,
                      class: "block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-b-xl",
                      data: { confirm: "Are you sure you want to delete this automation?" } do %>
                    Delete
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <%# Pagination %>
      <% if respond_to?(:paginate) %>
        <div class="mt-8 flex justify-center">
          <%= paginate @automations, theme: 'twitter-bootstrap-4' %>
        </div>
      <% end %>

    <% else %>
      <%# Empty State %>
      <div class="text-center py-16">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border-0 max-w-md mx-auto">
          <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-2xl flex items-center justify-center">
            <svg class="w-12 h-12 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-2">No automations yet</h3>
          <p class="text-gray-600 mb-6">Create your first email automation to start engaging with your audience automatically.</p>
          
          <%= link_to new_automation_path, 
              class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Create Your First Automation
          <% end %>
          
          <div class="mt-6 text-sm text-gray-500">
            <p>Popular automation types:</p>
            <div class="flex flex-wrap justify-center gap-2 mt-2">
              <span class="px-3 py-1 bg-gray-100 rounded-full text-xs">Welcome Series</span>
              <span class="px-3 py-1 bg-gray-100 rounded-full text-xs">Abandoned Cart</span>
              <span class="px-3 py-1 bg-gray-100 rounded-full text-xs">Re-engagement</span>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

<%# Quick Stats Overview %>
<% if @automations.any? %>
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border-0">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Automations</p>
          <p class="text-2xl font-bold text-gray-900"><%= @automations.count %></p>
        </div>
      </div>
    </div>

    <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border-0">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Active</p>
          <p class="text-2xl font-bold text-gray-900"><%= @automations.status_active.count %></p>
        </div>
      </div>
    </div>

    <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border-0">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Enrolled</p>
          <p class="text-2xl font-bold text-gray-900"><%= @automations.sum(&:total_enrollments) %></p>
        </div>
      </div>
    </div>

    <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border-0">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Avg. Completion</p>
          <p class="text-2xl font-bold text-gray-900">
            <%= @automations.any? ? (@automations.sum(&:completion_rate) / @automations.count).round(1) : 0 %>%
          </p>
        </div>
      </div>
    </div>
  </div>
<% end %>

<%# JavaScript for bulk actions and interactions %>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const checkboxes = document.querySelectorAll('.automation-checkbox');
  const bulkActionsBar = document.getElementById('bulk-actions-bar');
  const selectedCount = document.getElementById('selected-count');
  const clearSelection = document.getElementById('clear-selection');

  function updateBulkActions() {
    const selected = document.querySelectorAll('.automation-checkbox:checked');
    const count = selected.length;

    if (count > 0) {
      bulkActionsBar.classList.remove('hidden');
      selectedCount.textContent = `${count} selected`;
    } else {
      bulkActionsBar.classList.add('hidden');
    }
  }

  checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActions);
  });

  clearSelection.addEventListener('click', function() {
    checkboxes.forEach(checkbox => checkbox.checked = false);
    updateBulkActions();
  });

  // Bulk action handlers
  ['bulk-activate', 'bulk-pause', 'bulk-delete'].forEach(actionId => {
    const button = document.getElementById(actionId);
    if (button) {
      button.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.automation-checkbox:checked'))
          .map(cb => cb.dataset.automationId);

        if (selected.length === 0) return;

        const action = actionId.replace('bulk-', '');
        const confirmMessage = `Are you sure you want to ${action} ${selected.length} automation(s)?`;

        if (confirm(confirmMessage)) {
          // Show loading state
          this.disabled = true;
          this.textContent = 'Processing...';

          // Submit bulk action
          fetch('/automations/bulk_action', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
            },
            body: JSON.stringify({
              automation_ids: selected,
              bulk_action: action
            })
          }).then(response => {
            if (response.ok) {
              location.reload();
            } else {
              alert('An error occurred. Please try again.');
              this.disabled = false;
              this.textContent = action.charAt(0).toUpperCase() + action.slice(1);
            }
          }).catch(error => {
            alert('An error occurred. Please try again.');
            this.disabled = false;
            this.textContent = action.charAt(0).toUpperCase() + action.slice(1);
          });
        }
      });
    }
  });

  // Auto-submit search form with debounce
  const searchInput = document.querySelector('input[name="search"]');
  if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener('input', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        this.form.submit();
      }, 500);
    });
  }

  // Add loading states to action buttons
  document.querySelectorAll('a[data-method]').forEach(link => {
    link.addEventListener('click', function() {
      if (this.dataset.method === 'patch' || this.dataset.method === 'post') {
        this.classList.add('opacity-50', 'pointer-events-none');
        this.textContent = 'Processing...';
      }
    });
  });

  // Smooth animations for cards
  const cards = document.querySelectorAll('.grid > div');
  cards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.1}s`;
    card.classList.add('animate-fade-in-up');
  });
});
</script>

<%# CSS for animations %>
<style>
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for better aesthetics */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive improvements */
@media (max-width: 640px) {
  .grid-cols-1.lg\\:grid-cols-2.xl\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .flex.flex-col.sm\\:flex-row {
    flex-direction: column;
    gap: 1rem;
  }

  .px-6.py-3 {
    padding: 0.75rem 1rem;
  }
}

/* Loading spinner for buttons */
.loading::after {
  content: "";
  width: 16px;
  height: 16px;
  margin-left: 8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
