<% content_for :title, "Subscription & Billing" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Subscription & Billing</h1>
    <p class="mt-2 text-gray-600">Manage your subscription plan and billing information</p>
  </div>

  <!-- Current Plan Card -->
  <div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900">Current Plan</h2>
    </div>
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-xl font-semibold text-gray-900 capitalize"><%= @current_plan %></h3>
          <% if @subscription&.active? %>
            <p class="text-sm text-gray-600">
              <%= @available_plans[@current_plan][:price] > 0 ? "$#{@available_plans[@current_plan][:price] / 100}/month" : "Free" %>
            </p>
            <% if @subscription.trial_end && @subscription.trial_end > Time.current %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Trial until <%= @subscription.trial_end.strftime("%B %d, %Y") %>
              </span>
            <% end %>
            <% if @subscription.cancel_at_period_end? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Cancels on <%= @subscription.current_period_end.strftime("%B %d, %Y") %>
              </span>
            <% end %>
          <% end %>
        </div>
        <div class="text-right">
          <% if @subscription&.active? && @subscription.current_period_end %>
            <p class="text-sm text-gray-600">
              Next billing: <%= @subscription.current_period_end.strftime("%B %d, %Y") %>
            </p>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Usage Statistics -->
  <div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-medium text-gray-900">Usage This Period</h2>
        <%= form_with url: refresh_usage_subscription_path, method: :post, local: true, class: "inline" do |form| %>
          <%= form.submit "Refresh", class: "text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded" %>
        <% end %>
      </div>
    </div>
    <div class="px-6 py-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Contacts Usage -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Contacts</span>
            <span class="text-sm text-gray-600">
              <%= @usage_stats[:contacts][:used] %> / 
              <%= @usage_stats[:contacts][:limit] == Float::INFINITY ? "∞" : @usage_stats[:contacts][:limit] %>
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <% percentage = [@usage_stats[:contacts][:percentage], 100].min %>
            <div class="h-2 rounded-full <%= percentage >= 100 ? 'bg-red-500' : percentage >= 80 ? 'bg-yellow-500' : 'bg-green-500' %>" 
                 style="width: <%= percentage %>%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1"><%= percentage.round(1) %>% used</p>
        </div>

        <!-- Campaigns Usage -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Campaigns</span>
            <span class="text-sm text-gray-600">
              <%= @usage_stats[:campaigns][:used] %> / 
              <%= @usage_stats[:campaigns][:limit] == Float::INFINITY ? "∞" : @usage_stats[:campaigns][:limit] %>
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <% percentage = [@usage_stats[:campaigns][:percentage], 100].min %>
            <div class="h-2 rounded-full <%= percentage >= 100 ? 'bg-red-500' : percentage >= 80 ? 'bg-yellow-500' : 'bg-green-500' %>" 
                 style="width: <%= percentage %>%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1"><%= percentage.round(1) %>% used</p>
        </div>

        <!-- Templates Usage -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Templates</span>
            <span class="text-sm text-gray-600">
              <%= @usage_stats[:templates][:used] %> / 
              <%= @usage_stats[:templates][:limit] == Float::INFINITY ? "∞" : @usage_stats[:templates][:limit] %>
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <% percentage = [@usage_stats[:templates][:percentage], 100].min %>
            <div class="h-2 rounded-full <%= percentage >= 100 ? 'bg-red-500' : percentage >= 80 ? 'bg-yellow-500' : 'bg-green-500' %>" 
                 style="width: <%= percentage %>%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1"><%= percentage.round(1) %>% used</p>
        </div>

        <!-- Period Info -->
        <div>
          <div class="text-sm font-medium text-gray-700 mb-2">Billing Period</div>
          <% if @usage_stats[:period][:start] && @usage_stats[:period][:end] %>
            <p class="text-xs text-gray-600">
              <%= @usage_stats[:period][:start].strftime("%b %d") %> - 
              <%= @usage_stats[:period][:end].strftime("%b %d, %Y") %>
            </p>
            <% if @usage_stats[:period][:days_remaining] %>
              <p class="text-xs text-gray-500 mt-1">
                <%= @usage_stats[:period][:days_remaining] %> days remaining
              </p>
            <% end %>
          <% else %>
            <p class="text-xs text-gray-500">No active period</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Available Plans -->
  <div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900">Available Plans</h2>
    </div>
    <div class="px-6 py-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <% @available_plans.each do |plan_name, plan_config| %>
          <div class="border rounded-lg p-4 <%= @current_plan == plan_name ? 'border-blue-500 bg-blue-50' : 'border-gray-200' %>">
            <div class="text-center">
              <h3 class="text-lg font-semibold text-gray-900 capitalize"><%= plan_name %></h3>
              <p class="text-2xl font-bold text-gray-900 mt-2">
                <%= plan_config[:price] > 0 ? "$#{plan_config[:price] / 100}" : "Free" %>
                <% if plan_config[:price] > 0 %>
                  <span class="text-sm font-normal text-gray-600">/month</span>
                <% end %>
              </p>
              
              <!-- Plan Features -->
              <ul class="mt-4 space-y-2 text-sm text-gray-600">
                <li><%= plan_config[:limits][:contacts] == Float::INFINITY ? "Unlimited" : plan_config[:limits][:contacts] %> contacts</li>
                <li><%= plan_config[:limits][:campaigns_per_month] == Float::INFINITY ? "Unlimited" : plan_config[:limits][:campaigns_per_month] %> campaigns/month</li>
                <li><%= plan_config[:limits][:templates] == Float::INFINITY ? "Unlimited" : plan_config[:limits][:templates] %> templates</li>
              </ul>
              
              <!-- Action Button -->
              <div class="mt-4">
                <% if @current_plan == plan_name %>
                  <span class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100">
                    Current Plan
                  </span>
                <% elsif plan_name == 'free' %>
                  <%= form_with url: change_plan_subscription_path, method: :patch, local: true, class: "inline" do |form| %>
                    <%= form.hidden_field "plan_change[plan_name]", value: plan_name %>
                    <%= form.submit "Downgrade to Free", 
                        class: "w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded text-sm",
                        data: { confirm: "Are you sure you want to downgrade to the free plan? This will cancel your current subscription." } %>
                  <% end %>
                <% else %>
                  <%= form_with url: change_plan_subscription_path, method: :patch, local: true, class: "inline" do |form| %>
                    <%= form.hidden_field "plan_change[plan_name]", value: plan_name %>
                    <%= form.submit "Upgrade to #{plan_name.capitalize}", 
                        class: "w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded text-sm" %>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Subscription Actions -->
  <% if @subscription&.active? && @current_plan != 'free' %>
    <div class="bg-white shadow rounded-lg mb-8">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Subscription Actions</h2>
      </div>
      <div class="px-6 py-4">
        <div class="flex flex-wrap gap-4">
          <!-- Cancel/Reactivate -->
          <% if @subscription.cancel_at_period_end? %>
            <%= form_with url: reactivate_subscription_path, method: :patch, local: true, class: "inline" do |form| %>
              <%= form.submit "Reactivate Subscription", 
                  class: "bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded" %>
            <% end %>
          <% else %>
            <%= form_with url: cancel_subscription_path, method: :patch, local: true, class: "inline" do |form| %>
              <%= form.submit "Cancel at Period End", 
                  class: "bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded",
                  data: { confirm: "Are you sure you want to cancel your subscription? It will remain active until the end of your current billing period." } %>
            <% end %>
            
            <%= form_with url: cancel_subscription_path, method: :patch, local: true, class: "inline" do |form| %>
              <%= form.hidden_field :immediate, value: true %>
              <%= form.submit "Cancel Immediately", 
                  class: "bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded",
                  data: { confirm: "Are you sure you want to cancel your subscription immediately? This action cannot be undone and you will lose access to paid features right away." } %>
            <% end %>
          <% end %>
          
          <!-- Billing Portal -->
          <%= form_with url: billing_path + "/create_portal_session", method: :post, local: true, class: "inline" do |form| %>
            <%= form.submit "Manage Billing", 
                class: "bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded" %>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Payment Method Section (for paid plans) -->
  <% if @current_plan != 'free' %>
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Payment Method</h2>
      </div>
      <div class="px-6 py-4">
        <div id="payment-method-section">
          <!-- Payment method form will be loaded here via JavaScript -->
          <p class="text-gray-600">Loading payment methods...</p>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- Stripe Elements for payment method updates -->
<% if @current_plan != 'free' %>
  <script src="https://js.stripe.com/v3/"></script>
  <script>
    // Initialize Stripe
    const stripe = Stripe('<%= Rails.application.credentials.stripe[:publishable_key] || ENV['STRIPE_PUBLISHABLE_KEY'] %>');
    const elements = stripe.elements();
    
    // Load payment methods on page load
    document.addEventListener('DOMContentLoaded', function() {
      loadPaymentMethods();
    });
    
    function loadPaymentMethods() {
      fetch('/billing/payment_methods', {
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        renderPaymentMethods(data.payment_methods, data.default);
      })
      .catch(error => {
        console.error('Error loading payment methods:', error);
        document.getElementById('payment-method-section').innerHTML = 
          '<p class="text-red-600">Error loading payment methods. Please refresh the page.</p>';
      });
    }
    
    function renderPaymentMethods(paymentMethods, defaultPaymentMethod) {
      const section = document.getElementById('payment-method-section');
      
      if (paymentMethods.length === 0) {
        section.innerHTML = `
          <div class="text-center py-4">
            <p class="text-gray-600 mb-4">No payment methods on file.</p>
            <button onclick="showAddPaymentMethodForm()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
              Add Payment Method
            </button>
          </div>
        `;
      } else {
        let html = '<div class="space-y-4">';
        
        paymentMethods.forEach(pm => {
          const isDefault = pm.id === defaultPaymentMethod;
          html += `
            <div class="flex items-center justify-between p-4 border rounded-lg ${isDefault ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">**** **** **** ${pm.card.last4}</p>
                  <p class="text-sm text-gray-500">${pm.card.brand.toUpperCase()} • Expires ${pm.card.exp_month}/${pm.card.exp_year}</p>
                </div>
                ${isDefault ? '<span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Default</span>' : ''}
              </div>
              <div class="flex items-center space-x-2">
                ${!isDefault ? `<button onclick="setDefaultPaymentMethod('${pm.id}')" class="text-sm text-blue-600 hover:text-blue-800">Set as Default</button>` : ''}
                <button onclick="removePaymentMethod('${pm.id}')" class="text-sm text-red-600 hover:text-red-800">Remove</button>
              </div>
            </div>
          `;
        });
        
        html += `
          </div>
          <div class="mt-4">
            <button onclick="showAddPaymentMethodForm()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
              Add New Payment Method
            </button>
          </div>
        `;
        
        section.innerHTML = html;
      }
    }
    
    function showAddPaymentMethodForm() {
      // Implementation for adding payment method form
      // This would typically show a modal with Stripe Elements
      alert('Add payment method functionality would be implemented here with Stripe Elements');
    }
    
    function setDefaultPaymentMethod(paymentMethodId) {
      fetch(`/billing/payment_methods/${paymentMethodId}/set_default`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          loadPaymentMethods(); // Reload to show updated default
        } else {
          alert('Error setting default payment method: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while setting the default payment method.');
      });
    }
    
    function removePaymentMethod(paymentMethodId) {
      if (confirm('Are you sure you want to remove this payment method?')) {
        fetch(`/billing/payment_methods/${paymentMethodId}`, {
          method: 'DELETE',
          headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.status === 'success') {
            loadPaymentMethods(); // Reload to show updated list
          } else {
            alert('Error removing payment method: ' + data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('An error occurred while removing the payment method.');
        });
      }
    }
  </script>
<% end %>