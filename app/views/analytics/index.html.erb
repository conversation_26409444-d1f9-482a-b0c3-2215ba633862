<div class="min-h-full">
  <!-- Page header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Analytics Dashboard
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Track your email campaign performance and engagement metrics.
      </p>
    </div>
    <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
      <%= form_with url: analytics_path, method: :get, local: true, class: "flex items-center space-x-2" do |form| %>
        <%= form.select :date_range, 
            options_for_select([
              ['Last 7 days', '7d'],
              ['Last 30 days', '30d'],
              ['Last 90 days', '90d'],
              ['This year', '1y']
            ], params[:date_range] || '30d'),
            {},
            { class: "block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" } %>
        <%= form.submit "Update", 
            class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      <% end %>
      
      <%= link_to analytics_export_path(format: :csv, date_range: params[:date_range]), 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
        Export
      <% end %>
    </div>
  </div>

  <!-- Overview Stats -->
  <div class="mt-8">
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Total Campaigns -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Campaigns</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @overview_stats[:total_campaigns] %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Emails Sent -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Emails Sent</dt>
                <dd class="text-lg font-medium text-gray-900"><%= number_with_delimiter(@overview_stats[:total_sent]) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Average Open Rate -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Avg Open Rate</dt>
                <dd class="text-lg font-medium text-gray-900"><%= number_to_percentage(@overview_stats[:avg_open_rate], precision: 1) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Contacts -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Contacts</dt>
                <dd class="text-lg font-medium text-gray-900"><%= number_with_delimiter(@overview_stats[:total_contacts]) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
    <!-- Engagement Trends Chart -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Engagement Trends</h3>
        <p class="mt-1 text-sm text-gray-500">Email opens and clicks over time</p>
        <div class="mt-6">
          <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div class="text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">Chart visualization would go here</p>
              <p class="text-xs text-gray-400">Integration with charting library needed</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Campaign Performance -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Campaign Performance</h3>
        <p class="mt-1 text-sm text-gray-500">Top performing campaigns by open rate</p>
        <div class="mt-6">
          <% if @campaign_performance.any? %>
            <div class="space-y-4">
              <% @campaign_performance.each_with_index do |campaign, index| %>
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center">
                      <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-800 text-xs font-medium mr-3">
                        <%= index + 1 %>
                      </span>
                      <div class="min-w-0 flex-1">
                        <%= link_to campaign_path(campaign[:id]), class: "text-sm font-medium text-gray-900 hover:text-indigo-600 truncate" do %>
                          <%= campaign[:name] %>
                        <% end %>
                        <p class="text-xs text-gray-500 truncate"><%= campaign[:subject] %></p>
                      </div>
                    </div>
                  </div>
                  <div class="ml-4 flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-gray-900">
                      <%= number_to_percentage(campaign[:open_rate], precision: 1) %>
                    </div>
                    <div class="text-xs text-gray-500">
                      <%= pluralize(campaign[:opens], 'open') %> / <%= pluralize(campaign[:sent], 'sent') %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-6">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">No campaigns found for the selected period.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Tables -->
  <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
    <!-- Recent Campaign Performance -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Campaigns</h3>
          <%= link_to analytics_campaigns_path, class: "text-sm text-indigo-600 hover:text-indigo-500" do %>
            View all →
          <% end %>
        </div>
        <div class="mt-6">
          <% if @campaign_performance.any? %>
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <div class="min-w-full overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
                      <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent</th>
                      <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opens</th>
                      <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <% @campaign_performance.first(5).each do |campaign_data| %>
                      <tr>
                        <td class="px-3 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900 truncate max-w-32">
                            <%= link_to campaign_data[:name], campaign_path(campaign_data[:id]), class: "hover:text-indigo-600" %>
                          </div>
                          <div class="text-xs text-gray-500"><%= time_ago_in_words(campaign_data[:sent_at]) %> ago</div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          <%= number_with_delimiter(campaign_data[:emails_sent]) %>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          <%= number_with_delimiter(campaign_data[:opens]) %>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          <%= number_to_percentage(campaign_data[:open_rate], precision: 1) %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          <% else %>
            <div class="text-center py-6">
              <p class="text-sm text-gray-500">No recent campaigns found.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Contact Growth -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Contact Growth</h3>
          <%= link_to analytics_contacts_path, class: "text-sm text-indigo-600 hover:text-indigo-500" do %>
            View details →
          <% end %>
        </div>
        <div class="mt-6">
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
              <div class="text-2xl font-semibold text-green-600"><%= @contact_growth[:new_contacts] %></div>
              <div class="text-sm text-gray-500">New contacts</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-semibold text-red-600"><%= @contact_growth[:unsubscribed] %></div>
              <div class="text-sm text-gray-500">Unsubscribed</div>
            </div>
          </div>
          
          <div class="mt-6">
            <div class="text-sm text-gray-500 mb-2">Subscription Status</div>
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700">Subscribed</span>
                <span class="text-sm font-medium text-gray-900"><%= number_with_delimiter(@contact_growth[:subscribed]) %></span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700">Unsubscribed</span>
                <span class="text-sm font-medium text-gray-900"><%= number_with_delimiter(@contact_growth[:total_unsubscribed]) %></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>