<div class="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 py-8">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
            Team Management
          </h1>
          <p class="mt-2 text-gray-600">Manage team members, roles, and permissions</p>
        </div>
        <div class="flex items-center space-x-3">
          <%= link_to account_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Account
          <% end %>
          <button class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-medium rounded-lg hover:from-emerald-600 hover:to-teal-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" onclick="openInviteModal()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Invite Member
          </button>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
      <!-- Team Overview -->
      <div class="lg:col-span-1">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
          <div class="bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4">
            <h2 class="text-xl font-semibold text-white flex items-center">
              <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              Team Stats
            </h2>
          </div>
          
          <div class="p-6 space-y-6">
            <div class="text-center">
              <div class="text-3xl font-bold text-emerald-600"><%= @account.users.count %></div>
              <div class="text-sm text-gray-500">Total Members</div>
            </div>
            
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Owners</span>
                <span class="font-medium"><%= @account.users.where(role: 'owner').count %></span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Admins</span>
                <span class="font-medium"><%= @account.users.where(role: 'admin').count %></span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Members</span>
                <span class="font-medium"><%= @account.users.where(role: 'member').count %></span>
              </div>
            </div>
            
            <div class="pt-4 border-t border-gray-200">
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-900">Plan Limit</div>
                <div class="text-sm text-gray-500">
                  <%= @account.users.count %>/<%= @account.plan_limits[:team_members] %> members
                </div>
                <div class="mt-2">
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full" style="width: <%= [@account.users.count.to_f / @account.plan_limits[:team_members] * 100, 100].min %>%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Role Permissions -->
        <div class="mt-6 bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4">
            <h2 class="text-xl font-semibold text-white flex items-center">
              <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
              Permissions
            </h2>
          </div>
          
          <div class="p-6 space-y-4">
            <div>
              <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                <span class="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                Owner
              </h4>
              <ul class="text-sm text-gray-600 space-y-1 ml-5">
                <li>• Full account access</li>
                <li>• Billing management</li>
                <li>• Team management</li>
                <li>• Delete account</li>
              </ul>
            </div>
            
            <div>
              <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                <span class="w-3 h-3 bg-orange-500 rounded-full mr-2"></span>
                Admin
              </h4>
              <ul class="text-sm text-gray-600 space-y-1 ml-5">
                <li>• Manage campaigns</li>
                <li>• Manage contacts</li>
                <li>• Invite team members</li>
                <li>• View analytics</li>
              </ul>
            </div>
            
            <div>
              <h4 class="font-semibold text-gray-900 mb-2 flex items-center">
                <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                Member
              </h4>
              <ul class="text-sm text-gray-600 space-y-1 ml-5">
                <li>• View campaigns</li>
                <li>• Create templates</li>
                <li>• Basic analytics</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Team Members List -->
      <div class="lg:col-span-3">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
          <div class="bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4">
            <h2 class="text-xl font-semibold text-white flex items-center">
              <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
              Team Members
            </h2>
          </div>
          
          <div class="p-6">
            <div class="space-y-4">
              <% @account.users.includes(:account).each do |user| %>
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                  <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full flex items-center justify-center text-white font-semibold text-lg mr-4">
                      <%= user.first_name&.first&.upcase %><%= user.last_name&.first&.upcase %>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900"><%= user.full_name %></div>
                      <div class="text-sm text-gray-500"><%= user.email %></div>
                      <div class="text-xs text-gray-400 mt-1">
                        Joined <%= user.created_at.strftime("%B %Y") %>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-4">
                    <div class="text-right">
                      <div class="flex items-center">
                        <% case user.role %>
                        <% when 'owner' %>
                          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                            Owner
                          </span>
                        <% when 'admin' %>
                          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            <span class="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                            Admin
                          </span>
                        <% when 'member' %>
                          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                            Member
                          </span>
                        <% end %>
                      </div>
                      <div class="text-xs text-gray-400 mt-1">
                        Last active <%= time_ago_in_words(user.updated_at) %> ago
                      </div>
                    </div>
                    
                    <% if current_user.owner? && user != current_user %>
                      <div class="flex items-center space-x-2">
                        <div class="relative">
                          <button class="text-gray-400 hover:text-gray-600 p-2" onclick="toggleDropdown('<%= user.id %>')">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                            </svg>
                          </button>
                          
                          <div id="dropdown-<%= user.id %>" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                            <div class="py-1">
                              <% unless user.role == 'admin' %>
                                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                                  Make Admin
                                <% end %>
                              <% end %>
                              <% unless user.role == 'member' %>
                                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                                  Make Member
                                <% end %>
                              <% end %>
                              <div class="border-t border-gray-100"></div>
                              <%= link_to remove_user_account_path(user_id: user.id), method: :delete, 
                                  confirm: "Are you sure you want to remove #{user.full_name} from the team?",
                                  class: "block px-4 py-2 text-sm text-red-600 hover:bg-red-50" do %>
                                Remove from Team
                              <% end %>
                            </div>
                          </div>
                        </div>
                      </div>
                    <% elsif user == current_user %>
                      <span class="text-xs text-gray-400 px-3 py-1 bg-gray-100 rounded-full">You</span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Pending Invitations -->
        <% if @pending_invitations&.any? %>
          <div class="mt-8 bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
            <div class="bg-gradient-to-r from-yellow-500 to-orange-600 px-6 py-4">
              <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Pending Invitations
              </h2>
            </div>
            
            <div class="p-6">
              <div class="space-y-4">
                <% @pending_invitations.each do |invitation| %>
                  <div class="flex items-center justify-between p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                    <div class="flex items-center">
                      <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold text-lg mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                        </svg>
                      </div>
                      <div>
                        <div class="font-medium text-gray-900"><%= invitation.email %></div>
                        <div class="text-sm text-gray-600">Invited as <%= invitation.role.capitalize %></div>
                        <div class="text-xs text-gray-500 mt-1">
                          Sent <%= time_ago_in_words(invitation.created_at) %> ago
                        </div>
                      </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse"></span>
                        Pending
                      </span>
                      
                      <% if current_user.owner? %>
                        <%= link_to cancel_invitation_account_path(invitation_id: invitation.id), method: :delete,
                            confirm: "Are you sure you want to cancel this invitation?",
                            class: "text-red-600 hover:text-red-700 p-2" do %>
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Invite Modal -->
<div id="inviteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
    <div class="bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4 rounded-t-2xl">
      <h3 class="text-xl font-semibold text-white flex items-center">
        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
        </svg>
        Invite Team Member
      </h3>
    </div>
    
    <%= form_with url: invite_user_account_path, method: :post, local: true, class: "p-6 space-y-6" do |form| %>
      <div>
        <%= form.label :email, class: "block text-sm font-medium text-gray-700 mb-2" do %>
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
            </svg>
            Email Address
          </span>
        <% end %>
        <%= form.email_field :email, class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200", placeholder: "<EMAIL>", required: true %>
      </div>
      
      <div>
        <%= form.label :role, class: "block text-sm font-medium text-gray-700 mb-2" do %>
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Role
          </span>
        <% end %>
        <%= form.select :role, [['Member', 'member'], ['Admin', 'admin']], {}, 
            { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200" } %>
      </div>
      
      <div class="flex items-center justify-end space-x-4 pt-4">
        <button type="button" onclick="closeInviteModal()" class="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
          Cancel
        </button>
        <%= form.submit "Send Invitation", class: "px-8 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-medium rounded-lg hover:from-emerald-600 hover:to-teal-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
      </div>
    <% end %>
  </div>
</div>

<script>
  function openInviteModal() {
    document.getElementById('inviteModal').classList.remove('hidden');
  }
  
  function closeInviteModal() {
    document.getElementById('inviteModal').classList.add('hidden');
  }
  
  function toggleDropdown(userId) {
    const dropdown = document.getElementById(`dropdown-${userId}`);
    dropdown.classList.toggle('hidden');
  }
  
  // Close dropdowns when clicking outside
  document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
    dropdowns.forEach(dropdown => {
      if (!dropdown.contains(event.target) && !event.target.closest('button')) {
        dropdown.classList.add('hidden');
      }
    });
  });
  
  // Close modal when clicking outside
  document.getElementById('inviteModal').addEventListener('click', function(event) {
    if (event.target === this) {
      closeInviteModal();
    }
  });
</script>