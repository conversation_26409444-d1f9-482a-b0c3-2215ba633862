<div class="min-h-screen bg-gradient-to-br from-slate-50 via-indigo-50 to-purple-50 py-8">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Account Settings
          </h1>
          <p class="mt-2 text-gray-600">Configure your account preferences and settings</p>
        </div>
        <div class="flex items-center space-x-3">
          <%= link_to account_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Account
          <% end %>
        </div>
      </div>
    </div>

    <%= form_with model: @account, url: update_settings_account_path, method: :patch, local: true, class: "space-y-8" do |form| %>
      <!-- General Settings -->
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
          <h2 class="text-xl font-semibold text-white flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            General Settings
          </h2>
        </div>
        
        <div class="p-6 space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :timezone, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Timezone
                </span>
              <% end %>
              <%= form.select :timezone, 
                  options_for_select([
                    ['Pacific Time (PT)', 'America/Los_Angeles'],
                    ['Mountain Time (MT)', 'America/Denver'],
                    ['Central Time (CT)', 'America/Chicago'],
                    ['Eastern Time (ET)', 'America/New_York'],
                    ['UTC', 'UTC'],
                    ['London (GMT)', 'Europe/London'],
                    ['Paris (CET)', 'Europe/Paris'],
                    ['Tokyo (JST)', 'Asia/Tokyo'],
                    ['Sydney (AEST)', 'Australia/Sydney']
                  ], @account.timezone || 'America/New_York'), 
                  {}, 
                  { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200" } %>
            </div>

            <div>
              <%= form.label :date_format, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  Date Format
                </span>
              <% end %>
              <%= form.select :date_format, 
                  options_for_select([
                    ['MM/DD/YYYY', 'MM/DD/YYYY'],
                    ['DD/MM/YYYY', 'DD/MM/YYYY'],
                    ['YYYY-MM-DD', 'YYYY-MM-DD'],
                    ['Month DD, YYYY', 'Month DD, YYYY']
                  ], @account.date_format || 'MM/DD/YYYY'), 
                  {}, 
                  { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200" } %>
            </div>

            <div>
              <%= form.label :currency, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Currency
                </span>
              <% end %>
              <%= form.select :currency, 
                  options_for_select([
                    ['US Dollar (USD)', 'USD'],
                    ['Euro (EUR)', 'EUR'],
                    ['British Pound (GBP)', 'GBP'],
                    ['Canadian Dollar (CAD)', 'CAD'],
                    ['Australian Dollar (AUD)', 'AUD'],
                    ['Japanese Yen (JPY)', 'JPY']
                  ], @account.currency || 'USD'), 
                  {}, 
                  { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200" } %>
            </div>

            <div>
              <%= form.label :language, class: "block text-sm font-medium text-gray-700 mb-2" do %>
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                  </svg>
                  Language
                </span>
              <% end %>
              <%= form.select :language, 
                  options_for_select([
                    ['English', 'en'],
                    ['Spanish', 'es'],
                    ['French', 'fr'],
                    ['German', 'de'],
                    ['Italian', 'it'],
                    ['Portuguese', 'pt'],
                    ['Japanese', 'ja'],
                    ['Chinese (Simplified)', 'zh-CN']
                  ], @account.language || 'en'), 
                  {}, 
                  { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200" } %>
            </div>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4">
          <h2 class="text-xl font-semibold text-white flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2zM4 7h12V5H4v2z"></path>
            </svg>
            Notification Preferences
          </h2>
        </div>
        
        <div class="p-6 space-y-6">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Email Notifications</h4>
                <p class="text-sm text-gray-500">Receive email updates about your campaigns and account</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <%= form.check_box :email_notifications, 
                    { class: "sr-only peer", checked: @account.email_notifications != false }, 
                    "true", "false" %>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Campaign Updates</h4>
                <p class="text-sm text-gray-500">Get notified when campaigns are completed or need attention</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <%= form.check_box :campaign_notifications, 
                    { class: "sr-only peer", checked: @account.campaign_notifications != false }, 
                    "true", "false" %>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Team Activity</h4>
                <p class="text-sm text-gray-500">Receive notifications about team member activities</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <%= form.check_box :team_notifications, 
                    { class: "sr-only peer", checked: @account.team_notifications != false }, 
                    "true", "false" %>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Billing Alerts</h4>
                <p class="text-sm text-gray-500">Important billing and subscription notifications</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <%= form.check_box :billing_notifications, 
                    { class: "sr-only peer", checked: @account.billing_notifications != false }, 
                    "true", "false" %>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Marketing Updates</h4>
                <p class="text-sm text-gray-500">Product updates, tips, and promotional content</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <%= form.check_box :marketing_notifications, 
                    { class: "sr-only peer", checked: @account.marketing_notifications == true }, 
                    "true", "false" %>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Security Settings -->
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="bg-gradient-to-r from-red-500 to-pink-600 px-6 py-4">
          <h2 class="text-xl font-semibold text-white flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Security & Privacy
          </h2>
        </div>
        
        <div class="p-6 space-y-6">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Two-Factor Authentication</h4>
                <p class="text-sm text-gray-500">Add an extra layer of security to your account</p>
              </div>
              <div class="flex items-center space-x-3">
                <% if @account.two_factor_enabled? %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Enabled
                  </span>
                  <button type="button" class="text-sm text-red-600 hover:text-red-700 transition-colors duration-200">
                    Disable
                  </button>
                <% else %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <span class="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
                    Disabled
                  </span>
                  <button type="button" class="text-sm text-emerald-600 hover:text-emerald-700 transition-colors duration-200">
                    Enable
                  </button>
                <% end %>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Session Timeout</h4>
                <p class="text-sm text-gray-500">Automatically log out after period of inactivity</p>
              </div>
              <div class="w-48">
                <%= form.select :session_timeout, 
                    options_for_select([
                      ['15 minutes', 15],
                      ['30 minutes', 30],
                      ['1 hour', 60],
                      ['2 hours', 120],
                      ['4 hours', 240],
                      ['8 hours', 480],
                      ['Never', 0]
                    ], @account.session_timeout || 60), 
                    {}, 
                    { class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 text-sm" } %>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Data Export</h4>
                <p class="text-sm text-gray-500">Download a copy of your account data</p>
              </div>
              <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                Request Export
              </button>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">Account Deletion</h4>
                <p class="text-sm text-gray-500">Permanently delete your account and all data</p>
              </div>
              <button type="button" class="px-4 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 transition-colors duration-200">
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- API Settings -->
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4">
          <h2 class="text-xl font-semibold text-white flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
            API & Integrations
          </h2>
        </div>
        
        <div class="p-6 space-y-6">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-medium text-gray-900">API Access</h4>
              <p class="text-sm text-gray-500">Enable API access for third-party integrations</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <%= form.check_box :api_enabled, 
                  { class: "sr-only peer", checked: @account.api_enabled == true }, 
                  "true", "false" %>
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <% if @account.api_enabled? %>
            <div class="border-t border-gray-200 pt-6">
              <div class="space-y-4">
                <div>
                  <h4 class="font-medium text-gray-900 mb-2">API Key</h4>
                  <div class="flex items-center space-x-3">
                    <input type="text" value="sk-1234567890abcdef..." class="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 font-mono text-sm" readonly>
                    <button type="button" class="px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                      Copy
                    </button>
                    <button type="button" class="px-4 py-3 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 transition-colors duration-200">
                      Regenerate
                    </button>
                  </div>
                  <p class="text-xs text-gray-500 mt-2">Keep your API key secure and never share it publicly.</p>
                </div>

                <div>
                  <h4 class="font-medium text-gray-900 mb-2">Rate Limits</h4>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                      <div class="text-2xl font-bold text-blue-600">1,000</div>
                      <div class="text-sm text-gray-500">Requests/hour</div>
                    </div>
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                      <div class="text-2xl font-bold text-blue-600">10,000</div>
                      <div class="text-sm text-gray-500">Requests/day</div>
                    </div>
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                      <div class="text-2xl font-bold text-blue-600">100,000</div>
                      <div class="text-sm text-gray-500">Requests/month</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-end space-x-4 pt-6">
        <%= link_to account_path, class: "px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200" do %>
          Cancel
        <% end %>
        
        <%= form.submit "Save Settings", class: "px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
      </div>
    <% end %>
  </div>
</div>