<% content_for :title, "Account Settings - #{@account.name}" %>

<div class="px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
  <!-- Page header -->
  <div class="sm:flex sm:items-center pt-8">
    <div class="sm:flex-auto">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <svg class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Account Overview</h1>
      </div>
      <p class="mt-3 text-base text-slate-600">
        Manage your account settings, subscription, and team members with ease.
      </p>
    </div>
    <div class="mt-6 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to edit_account_path, 
          class: "inline-flex items-center justify-center rounded-xl border border-transparent bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-3 text-sm font-semibold text-white shadow-lg hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transform hover:scale-105 transition-all duration-200 sm:w-auto" do %>
        <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        Edit Account
      <% end %>
    </div>
  </div>

  <!-- Account Information Card -->
  <div class="mt-8">
    <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20">
      <div class="px-6 py-6 sm:p-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-slate-800">Account Information</h3>
        </div>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Account Name</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @account.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Company</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @account.company_name.presence || "Not specified" %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Website</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @account.website.present? %>
                <%= link_to @account.website, @account.website, target: "_blank", class: "text-indigo-600 hover:text-indigo-500" %>
              <% else %>
                Not specified
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-slate-500">Plan</dt>
            <dd class="mt-2">
              <% plan_class = case @account.plan
                             when 'free' then 'bg-gradient-to-r from-gray-500 to-gray-600 text-white'
                             when 'starter' then 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                             when 'professional' then 'bg-gradient-to-r from-purple-500 to-purple-600 text-white'
                             when 'enterprise' then 'bg-gradient-to-r from-amber-500 to-amber-600 text-white'
                             else 'bg-gradient-to-r from-gray-500 to-gray-600 text-white'
                             end %>
              <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold <%= plan_class %> shadow-lg">
                <svg class="-ml-0.5 mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
                <%= @account.plan.titleize %>
              </span>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-slate-500">Status</dt>
            <dd class="mt-2">
              <% status_class = case @account.status
                               when 'active' then 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white'
                               when 'suspended' then 'bg-gradient-to-r from-amber-500 to-amber-600 text-white'
                               when 'cancelled' then 'bg-gradient-to-r from-red-500 to-red-600 text-white'
                               else 'bg-gradient-to-r from-slate-500 to-slate-600 text-white'
                               end %>
              <% status_icon = case @account.status
                              when 'active' then 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                              when 'suspended' then 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z'
                              when 'cancelled' then 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
                              else 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                              end %>
              <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold <%= status_class %> shadow-lg">
                <svg class="-ml-0.5 mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= status_icon %>" />
                </svg>
                <%= @account.status.titleize %>
              </span>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Created</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @account.created_at.strftime("%B %d, %Y") %></dd>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Usage Statistics -->
  <div class="mt-8">
    <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20">
      <div class="px-6 py-6 sm:p-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-slate-800">Usage Statistics</h3>
        </div>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
          <!-- Contacts Usage -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200/50 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-semibold text-blue-700">Contacts</p>
                <p class="text-3xl font-bold text-blue-900 mt-1"><%= @usage_stats[:contacts_count] %></p>
              </div>
              <div class="flex-shrink-0">
                <div class="bg-blue-500 rounded-full p-3">
                  <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
            </div>
            <% if @account.plan_limits[:contacts] %>
              <div class="mt-4">
                <div class="flex items-center justify-between text-sm text-blue-700 font-medium">
                  <span>Limit: <%= number_with_delimiter(@account.plan_limits[:contacts]) %></span>
                  <span><%= ((@usage_stats[:contacts_count].to_f / @account.plan_limits[:contacts]) * 100).round(1) %>%</span>
                </div>
                <div class="mt-2 bg-blue-200 rounded-full h-3 overflow-hidden">
                  <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out" style="width: <%= [(@usage_stats[:contacts_count].to_f / @account.plan_limits[:contacts]) * 100, 100].min %>%"></div>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Campaigns Usage -->
          <div class="bg-gradient-to-br from-emerald-50 to-green-100 rounded-xl p-6 border border-emerald-200/50 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-semibold text-emerald-700">Campaigns This Month</p>
                <p class="text-3xl font-bold text-emerald-900 mt-1"><%= @usage_stats[:campaigns_this_month] %></p>
              </div>
              <div class="flex-shrink-0">
                <div class="bg-emerald-500 rounded-full p-3">
                  <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
            </div>
            <% if @account.plan_limits[:campaigns_per_month] %>
              <div class="mt-4">
                <div class="flex items-center justify-between text-sm text-emerald-700 font-medium">
                  <span>Limit: <%= @account.plan_limits[:campaigns_per_month] %></span>
                  <span><%= ((@usage_stats[:campaigns_this_month].to_f / @account.plan_limits[:campaigns_per_month]) * 100).round(1) %>%</span>
                </div>
                <div class="mt-2 bg-emerald-200 rounded-full h-3 overflow-hidden">
                  <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 h-3 rounded-full transition-all duration-500 ease-out" style="width: <%= [(@usage_stats[:campaigns_this_month].to_f / @account.plan_limits[:campaigns_per_month]) * 100, 100].min %>%"></div>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Templates Usage -->
          <div class="bg-gradient-to-br from-purple-50 to-violet-100 rounded-xl p-6 border border-purple-200/50 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-semibold text-purple-700">Templates</p>
                <p class="text-3xl font-bold text-purple-900 mt-1"><%= @usage_stats[:templates_count] %></p>
              </div>
              <div class="flex-shrink-0">
                <div class="bg-purple-500 rounded-full p-3">
                  <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
            </div>
            <% if @account.plan_limits[:templates] %>
              <div class="mt-4">
                <div class="flex items-center justify-between text-sm text-purple-700 font-medium">
                  <span>Limit: <%= @account.plan_limits[:templates] %></span>
                  <span><%= ((@usage_stats[:templates_count].to_f / @account.plan_limits[:templates]) * 100).round(1) %>%</span>
                </div>
                <div class="mt-2 bg-purple-200 rounded-full h-3 overflow-hidden">
                  <div class="bg-gradient-to-r from-purple-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out" style="width: <%= [(@usage_stats[:templates_count].to_f / @account.plan_limits[:templates]) * 100, 100].min %>%"></div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Subscription Information -->
  <% if @subscription %>
    <div class="mt-8">
      <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20">
        <div class="px-6 py-6 sm:p-8">
          <div class="flex items-center space-x-3 mb-6">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold text-slate-800">Subscription Details</h3>
          </div>
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div class="bg-gradient-to-br from-amber-50 to-orange-100 rounded-xl p-4 border border-amber-200/50">
              <dt class="text-sm font-semibold text-amber-700 flex items-center">
                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
                Current Plan
              </dt>
              <dd class="mt-2 text-lg font-bold text-amber-900"><%= @subscription.plan&.titleize || @account.plan.titleize %></dd>
            </div>
            <div class="bg-gradient-to-br from-emerald-50 to-green-100 rounded-xl p-4 border border-emerald-200/50">
              <dt class="text-sm font-semibold text-emerald-700 flex items-center">
                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Status
              </dt>
              <dd class="mt-2">
                <% sub_status_class = case @subscription.status
                                     when 'active' then 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white'
                                     when 'past_due' then 'bg-gradient-to-r from-amber-500 to-amber-600 text-white'
                                     when 'cancelled' then 'bg-gradient-to-r from-red-500 to-red-600 text-white'
                                     else 'bg-gradient-to-r from-slate-500 to-slate-600 text-white'
                                     end %>
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold <%= sub_status_class %> shadow-lg">
                  <%= @subscription.status&.titleize || 'Unknown' %>
                </span>
              </dd>
            </div>
            <% if @subscription.current_period_end %>
              <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-4 border border-blue-200/50">
                <dt class="text-sm font-semibold text-blue-700 flex items-center">
                  <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Next Billing Date
                </dt>
                <dd class="mt-2 text-lg font-bold text-blue-900"><%= @subscription.current_period_end.strftime("%B %d, %Y") %></dd>
              </div>
            <% end %>
            <% if @subscription.amount %>
              <div class="bg-gradient-to-br from-purple-50 to-violet-100 rounded-xl p-4 border border-purple-200/50">
                <dt class="text-sm font-semibold text-purple-700 flex items-center">
                  <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Monthly Cost
                </dt>
                <dd class="mt-2 text-lg font-bold text-purple-900">$<%= number_with_precision(@subscription.amount / 100.0, precision: 2) %></dd>
              </div>
            <% end %>
          </div>
          <div class="mt-8">
            <%= link_to billing_account_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent shadow-lg text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transform hover:scale-105 transition-all duration-200" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              Manage Billing
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Team Members -->
  <div class="mt-8">
    <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20">
      <div class="px-6 py-6 sm:p-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-slate-800">Team Members</h3>
        </div>
        <div class="flow-root">
          <ul role="list" class="space-y-4">
            <% @team_members.limit(5).each do |user| %>
              <li class="bg-gradient-to-r from-slate-50 to-gray-100 rounded-xl p-4 border border-slate-200/50 hover:shadow-md transition-all duration-300">
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div class="h-12 w-12 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <span class="text-lg font-bold text-white"><%= user.email.first.upcase %></span>
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-base font-semibold text-slate-900 truncate">
                      <%= user.email %>
                    </p>
                    <p class="text-sm text-slate-600 flex items-center mt-1">
                      <svg class="h-4 w-4 mr-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <%= user.role&.titleize || 'Member' %>
                    </p>
                  </div>
                  <div class="flex-shrink-0">
                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold bg-gradient-to-r from-slate-500 to-slate-600 text-white shadow-lg">
                      <%= user.role&.titleize || 'Member' %>
                    </span>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
        <% if @team_members.count > 5 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@team_members.count} members", team_account_path, 
                class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" %>
          </div>
        <% end %>
        <div class="mt-8">
          <%= link_to team_account_path, 
              class: "inline-flex items-center px-6 py-3 border border-transparent shadow-lg text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transform hover:scale-105 transition-all duration-200" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Manage Team
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="mt-8 mb-12">
    <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20">
      <div class="px-6 py-6 sm:p-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-rose-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold text-slate-800">Quick Actions</h3>
        </div>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <%= link_to edit_account_path, 
              class: "group relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100 border border-blue-200/50 rounded-xl p-6 hover:shadow-lg transform hover:scale-105 transition-all duration-300" do %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="bg-blue-500 rounded-full p-3">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-blue-900">Edit Account</h4>
                <p class="text-sm text-blue-700">Update account details</p>
              </div>
            </div>
          <% end %>

          <%= link_to billing_account_path, 
              class: "group relative overflow-hidden bg-gradient-to-br from-emerald-50 to-green-100 border border-emerald-200/50 rounded-xl p-6 hover:shadow-lg transform hover:scale-105 transition-all duration-300" do %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="bg-emerald-500 rounded-full p-3">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-emerald-900">Billing</h4>
                <p class="text-sm text-emerald-700">Manage subscription</p>
              </div>
            </div>
          <% end %>

          <%= link_to team_account_path, 
              class: "group relative overflow-hidden bg-gradient-to-br from-purple-50 to-violet-100 border border-purple-200/50 rounded-xl p-6 hover:shadow-lg transform hover:scale-105 transition-all duration-300" do %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="bg-purple-500 rounded-full p-3">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-purple-900">Team</h4>
                <p class="text-sm text-purple-700">Manage team members</p>
              </div>
            </div>
          <% end %>

          <%= link_to settings_account_path,
              class: "group relative overflow-hidden bg-gradient-to-br from-amber-50 to-orange-100 border border-amber-200/50 rounded-xl p-6 hover:shadow-lg transform hover:scale-105 transition-all duration-300" do %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="bg-amber-500 rounded-full p-3">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-amber-900">Settings</h4>
                <p class="text-sm text-amber-700">Account preferences</p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>