<% content_for :title, "Account Settings - #{@account.name}" %>

<div class="px-4 sm:px-6 lg:px-8">
  <!-- Page header -->
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-2xl font-semibold text-gray-900">Account Overview</h1>
      <p class="mt-2 text-sm text-gray-700">
        Manage your account settings, subscription, and team members.
      </p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to "Edit Account", edit_account_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto" %>
    </div>
  </div>

  <!-- Account Information Card -->
  <div class="mt-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Information</h3>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Account Name</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @account.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Company</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @account.company_name.presence || "Not specified" %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Website</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @account.website.present? %>
                <%= link_to @account.website, @account.website, target: "_blank", class: "text-indigo-600 hover:text-indigo-500" %>
              <% else %>
                Not specified
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Plan</dt>
            <dd class="mt-1">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <%= @account.plan.titleize %>
              </span>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Status</dt>
            <dd class="mt-1">
              <% status_class = case @account.status
                               when 'active' then 'bg-green-100 text-green-800'
                               when 'suspended' then 'bg-yellow-100 text-yellow-800'
                               when 'cancelled' then 'bg-red-100 text-red-800'
                               else 'bg-gray-100 text-gray-800'
                               end %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= status_class %>">
                <%= @account.status.titleize %>
              </span>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Created</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @account.created_at.strftime("%B %d, %Y") %></dd>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Usage Statistics -->
  <div class="mt-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Usage Statistics</h3>
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-3">
          <!-- Contacts Usage -->
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-500">Contacts</p>
                <p class="text-2xl font-semibold text-gray-900"><%= @usage_stats[:contacts_count] %></p>
              </div>
              <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <% if @account.plan_limits[:contacts] %>
              <div class="mt-2">
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>Limit: <%= number_with_delimiter(@account.plan_limits[:contacts]) %></span>
                  <span><%= ((@usage_stats[:contacts_count].to_f / @account.plan_limits[:contacts]) * 100).round(1) %>%</span>
                </div>
                <div class="mt-1 bg-gray-200 rounded-full h-2">
                  <div class="bg-indigo-600 h-2 rounded-full" style="width: <%= [(@usage_stats[:contacts_count].to_f / @account.plan_limits[:contacts]) * 100, 100].min %>%"></div>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Campaigns Usage -->
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-500">Campaigns This Month</p>
                <p class="text-2xl font-semibold text-gray-900"><%= @usage_stats[:campaigns_this_month] %></p>
              </div>
              <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <% if @account.plan_limits[:campaigns_per_month] %>
              <div class="mt-2">
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>Limit: <%= @account.plan_limits[:campaigns_per_month] %></span>
                  <span><%= ((@usage_stats[:campaigns_this_month].to_f / @account.plan_limits[:campaigns_per_month]) * 100).round(1) %>%</span>
                </div>
                <div class="mt-1 bg-gray-200 rounded-full h-2">
                  <div class="bg-indigo-600 h-2 rounded-full" style="width: <%= [(@usage_stats[:campaigns_this_month].to_f / @account.plan_limits[:campaigns_per_month]) * 100, 100].min %>%"></div>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Templates Usage -->
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-500">Templates</p>
                <p class="text-2xl font-semibold text-gray-900"><%= @usage_stats[:templates_count] %></p>
              </div>
              <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <% if @account.plan_limits[:templates] %>
              <div class="mt-2">
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>Limit: <%= @account.plan_limits[:templates] %></span>
                  <span><%= ((@usage_stats[:templates_count].to_f / @account.plan_limits[:templates]) * 100).round(1) %>%</span>
                </div>
                <div class="mt-1 bg-gray-200 rounded-full h-2">
                  <div class="bg-indigo-600 h-2 rounded-full" style="width: <%= [(@usage_stats[:templates_count].to_f / @account.plan_limits[:templates]) * 100, 100].min %>%"></div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Subscription Information -->
  <% if @subscription %>
    <div class="mt-8">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Subscription</h3>
            <%= link_to "Manage Billing", billing_account_path, 
                class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" %>
          </div>
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Current Plan</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @subscription.plan&.titleize || @account.plan.titleize %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1">
                <% sub_status_class = case @subscription.status
                                     when 'active' then 'bg-green-100 text-green-800'
                                     when 'past_due' then 'bg-yellow-100 text-yellow-800'
                                     when 'cancelled' then 'bg-red-100 text-red-800'
                                     else 'bg-gray-100 text-gray-800'
                                     end %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= sub_status_class %>">
                  <%= @subscription.status&.titleize || 'Unknown' %>
                </span>
              </dd>
            </div>
            <% if @subscription.current_period_end %>
              <div>
                <dt class="text-sm font-medium text-gray-500">Next Billing Date</dt>
                <dd class="mt-1 text-sm text-gray-900"><%= @subscription.current_period_end.strftime("%B %d, %Y") %></dd>
              </div>
            <% end %>
            <% if @subscription.amount %>
              <div>
                <dt class="text-sm font-medium text-gray-500">Monthly Cost</dt>
                <dd class="mt-1 text-sm text-gray-900">$<%= number_with_precision(@subscription.amount / 100.0, precision: 2) %></dd>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Team Members -->
  <div class="mt-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Team Members</h3>
          <%= link_to "Manage Team", team_account_path, 
              class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" %>
        </div>
        <div class="flow-root">
          <ul role="list" class="-my-5 divide-y divide-gray-200">
            <% @team_members.limit(5).each do |user| %>
              <li class="py-4">
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
                      <span class="text-sm font-medium text-white"><%= user.email.first.upcase %></span>
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      <%= user.email %>
                    </p>
                    <p class="text-sm text-gray-500 truncate">
                      <%= user.role&.titleize || 'Member' %>
                    </p>
                  </div>
                  <div class="flex-shrink-0">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <%= user.role&.titleize || 'Member' %>
                    </span>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
        <% if @team_members.count > 5 %>
          <div class="mt-4 text-center">
            <%= link_to "View all #{@team_members.count} members", team_account_path, 
                class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="mt-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <%= link_to edit_account_path, 
              class: "relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <span class="absolute inset-0" aria-hidden="true"></span>
              <p class="text-sm font-medium text-gray-900">Edit Account</p>
              <p class="text-sm text-gray-500">Update account details</p>
            </div>
          <% end %>

          <%= link_to billing_account_path, 
              class: "relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <span class="absolute inset-0" aria-hidden="true"></span>
              <p class="text-sm font-medium text-gray-900">Billing</p>
              <p class="text-sm text-gray-500">Manage subscription</p>
            </div>
          <% end %>

          <%= link_to team_account_path, 
              class: "relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <span class="absolute inset-0" aria-hidden="true"></span>
              <p class="text-sm font-medium text-gray-900">Team</p>
              <p class="text-sm text-gray-500">Manage team members</p>
            </div>
          <% end %>

          <%= link_to account_settings_path, 
              class: "relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <span class="absolute inset-0" aria-hidden="true"></span>
              <p class="text-sm font-medium text-gray-900">Settings</p>
              <p class="text-sm text-gray-500">Account preferences</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>