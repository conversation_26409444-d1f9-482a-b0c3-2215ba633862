# frozen_string_literal: true

class BillingController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_account_access
  before_action :ensure_stripe_customer

  # GET /billing
  def index
    @subscription = current_account.subscription
    @usage_service = UsageTrackingService.new(account: current_account)
    @usage_stats = @usage_service.usage_stats
    @available_plans = STRIPE_PLANS
    @current_plan = current_account.plan
    
    # Get recent invoices
    @invoices = fetch_recent_invoices
    
    # Get payment methods
    @payment_methods = fetch_payment_methods
  end

  # GET /billing/invoices
  def invoices
    @invoices = fetch_invoices(limit: params[:limit]&.to_i || 20)
    
    respond_to do |format|
      format.html
      format.json { render json: @invoices }
    end
  end

  # GET /billing/invoices/:invoice_id
  def invoice
    @invoice = fetch_invoice(params[:invoice_id])
    
    if @invoice
      respond_to do |format|
        format.html
        format.json { render json: @invoice }
        format.pdf { redirect_to @invoice.invoice_pdf }
      end
    else
      redirect_to billing_invoices_path, alert: 'Invoice not found.'
    end
  end

  # GET /billing/payment_methods
  def payment_methods
    @payment_methods = fetch_payment_methods
    @default_payment_method = fetch_default_payment_method
    
    respond_to do |format|
      format.html
      format.json { render json: { payment_methods: @payment_methods, default: @default_payment_method } }
    end
  end

  # POST /billing/payment_methods
  def create_payment_method
    begin
      payment_method = Stripe::PaymentMethod.attach(
        params[:payment_method_id],
        { customer: current_account.stripe_customer_id }
      )
      
      # Set as default if requested or if it's the first payment method
      if params[:set_as_default] == 'true' || fetch_payment_methods.empty?
        set_default_payment_method(params[:payment_method_id])
      end
      
      respond_to do |format|
        format.json { render json: { status: 'success', payment_method: payment_method } }
        format.html { redirect_to billing_payment_methods_path, notice: 'Payment method added successfully!' }
      end
    rescue Stripe::StripeError => e
      respond_to do |format|
        format.json { render json: { status: 'error', message: e.user_message || e.message }, status: :unprocessable_entity }
        format.html { redirect_to billing_payment_methods_path, alert: "Error: #{e.user_message || e.message}" }
      end
    end
  end

  # DELETE /billing/payment_methods/:payment_method_id
  def destroy_payment_method
    begin
      Stripe::PaymentMethod.detach(params[:payment_method_id])
      
      respond_to do |format|
        format.json { render json: { status: 'success' } }
        format.html { redirect_to billing_payment_methods_path, notice: 'Payment method removed successfully!' }
      end
    rescue Stripe::StripeError => e
      respond_to do |format|
        format.json { render json: { status: 'error', message: e.user_message || e.message }, status: :unprocessable_entity }
        format.html { redirect_to billing_payment_methods_path, alert: "Error: #{e.user_message || e.message}" }
      end
    end
  end

  # PATCH /billing/payment_methods/:payment_method_id/set_default
  def set_default_payment_method
    begin
      Stripe::Customer.update(
        current_account.stripe_customer_id,
        {
          invoice_settings: {
            default_payment_method: params[:payment_method_id]
          }
        }
      )
      
      respond_to do |format|
        format.json { render json: { status: 'success' } }
        format.html { redirect_to billing_payment_methods_path, notice: 'Default payment method updated!' }
      end
    rescue Stripe::StripeError => e
      respond_to do |format|
        format.json { render json: { status: 'error', message: e.user_message || e.message }, status: :unprocessable_entity }
        format.html { redirect_to billing_payment_methods_path, alert: "Error: #{e.user_message || e.message}" }
      end
    end
  end

  # GET /billing/upcoming_invoice
  def upcoming_invoice
    begin
      @upcoming_invoice = Stripe::Invoice.upcoming(
        customer: current_account.stripe_customer_id
      )
      
      respond_to do |format|
        format.html
        format.json { render json: @upcoming_invoice }
      end
    rescue Stripe::StripeError => e
      respond_to do |format|
        format.json { render json: { error: e.message }, status: :unprocessable_entity }
        format.html { redirect_to billing_path, alert: 'Unable to fetch upcoming invoice.' }
      end
    end
  end

  # POST /billing/create_portal_session
  def create_portal_session
    begin
      session = Stripe::BillingPortal::Session.create({
        customer: current_account.stripe_customer_id,
        return_url: billing_url
      })
      
      redirect_to session.url
    rescue Stripe::StripeError => e
      redirect_to billing_path, alert: "Error: #{e.user_message || e.message}"
    end
  end

  private

  def ensure_account_access
    unless current_user.can_manage_billing?(current_account)
      redirect_to root_path, alert: 'You do not have permission to manage billing for this account.'
    end
  end

  def ensure_stripe_customer
    unless current_account.stripe_customer_id.present?
      current_account.ensure_stripe_customer!
    end
  end

  def fetch_recent_invoices(limit = 5)
    fetch_invoices(limit: limit)
  end

  def fetch_invoices(limit: 20)
    return [] unless current_account.stripe_customer_id.present?
    
    begin
      invoices = Stripe::Invoice.list(
        customer: current_account.stripe_customer_id,
        limit: limit
      )
      invoices.data
    rescue Stripe::StripeError => e
      Rails.logger.error "Error fetching invoices: #{e.message}"
      []
    end
  end

  def fetch_invoice(invoice_id)
    return nil unless current_account.stripe_customer_id.present?
    
    begin
      invoice = Stripe::Invoice.retrieve(invoice_id)
      # Verify the invoice belongs to this customer
      invoice.customer == current_account.stripe_customer_id ? invoice : nil
    rescue Stripe::StripeError => e
      Rails.logger.error "Error fetching invoice #{invoice_id}: #{e.message}"
      nil
    end
  end

  def fetch_payment_methods
    return [] unless current_account.stripe_customer_id.present?
    
    begin
      payment_methods = Stripe::PaymentMethod.list(
        customer: current_account.stripe_customer_id,
        type: 'card'
      )
      payment_methods.data
    rescue Stripe::StripeError => e
      Rails.logger.error "Error fetching payment methods: #{e.message}"
      []
    end
  end

  def fetch_default_payment_method
    return nil unless current_account.stripe_customer_id.present?
    
    begin
      customer = Stripe::Customer.retrieve(current_account.stripe_customer_id)
      customer.invoice_settings&.default_payment_method
    rescue Stripe::StripeError => e
      Rails.logger.error "Error fetching default payment method: #{e.message}"
      nil
    end
  end
end