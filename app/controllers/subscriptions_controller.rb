# frozen_string_literal: true

class SubscriptionsController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_account_access
  before_action :set_subscription, only: [:show, :cancel, :reactivate]

  # GET /subscription
  def show
    @usage_service = UsageTrackingService.new(account: current_account)
    @usage_stats = @usage_service.usage_stats
    @available_plans = STRIPE_PLANS
    @current_plan = current_account.plan
  end

  # POST /subscription
  def create
    @service = SubscriptionService.new(account: current_account)
    
    result = @service.create_subscription(
      plan_name: subscription_params[:plan_name],
      payment_method_id: subscription_params[:payment_method_id],
      trial_days: subscription_params[:trial_days]&.to_i
    )

    if result.success?
      redirect_to subscription_path, notice: 'Subscription created successfully!'
    else
      redirect_to subscription_path, alert: result.error
    end
  end

  # PATCH /subscription/change_plan
  def change_plan
    @service = SubscriptionService.new(account: current_account)
    
    result = @service.change_plan(new_plan_name: plan_change_params[:plan_name])

    if result.success?
      redirect_to subscription_path, notice: 'Plan changed successfully!'
    else
      redirect_to subscription_path, alert: result.error
    end
  end

  # PATCH /subscription/cancel
  def cancel
    @service = SubscriptionService.new(account: current_account)
    
    result = @service.cancel_subscription(immediate: params[:immediate] == 'true')

    if result.success?
      message = params[:immediate] == 'true' ? 
                'Subscription canceled immediately.' : 
                'Subscription will be canceled at the end of the current period.'
      redirect_to subscription_path, notice: message
    else
      redirect_to subscription_path, alert: result.error
    end
  end

  # PATCH /subscription/reactivate
  def reactivate
    @service = SubscriptionService.new(account: current_account)
    
    result = @service.reactivate_subscription

    if result.success?
      redirect_to subscription_path, notice: 'Subscription reactivated successfully!'
    else
      redirect_to subscription_path, alert: result.error
    end
  end

  # PATCH /subscription/payment_method
  def update_payment_method
    @service = SubscriptionService.new(account: current_account)
    
    result = @service.update_payment_method(
      payment_method_id: payment_method_params[:payment_method_id]
    )

    if result.success?
      redirect_to subscription_path, notice: 'Payment method updated successfully!'
    else
      redirect_to subscription_path, alert: result.error
    end
  end

  # GET /subscription/usage
  def usage
    @usage_service = UsageTrackingService.new(account: current_account)
    @usage_stats = @usage_service.usage_stats
    
    respond_to do |format|
      format.json { render json: @usage_stats }
      format.html { redirect_to subscription_path }
    end
  end

  # POST /subscription/refresh_usage
  def refresh_usage
    @usage_service = UsageTrackingService.new(account: current_account)
    @usage_service.refresh_usage!
    
    respond_to do |format|
      format.json { render json: { status: 'refreshed' } }
      format.html { redirect_to subscription_path, notice: 'Usage refreshed successfully!' }
    end
  end

  private

  def set_subscription
    @subscription = current_account.subscription
  end

  def ensure_account_access
    unless current_user.can_manage_billing?(current_account)
      redirect_to root_path, alert: 'You do not have permission to manage billing for this account.'
    end
  end

  def subscription_params
    params.require(:subscription).permit(:plan_name, :payment_method_id, :trial_days)
  end

  def plan_change_params
    params.require(:plan_change).permit(:plan_name)
  end

  def payment_method_params
    params.require(:payment_method).permit(:payment_method_id)
  end
end