# frozen_string_literal: true

class StripeWebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token
  skip_before_action :authenticate_user!
  
  before_action :verify_stripe_signature

  # POST /stripe/webhooks
  def create
    case @event.type
    when 'customer.subscription.created'
      handle_subscription_created
    when 'customer.subscription.updated'
      handle_subscription_updated
    when 'customer.subscription.deleted'
      handle_subscription_deleted
    when 'invoice.payment_succeeded'
      handle_payment_succeeded
    when 'invoice.payment_failed'
      handle_payment_failed
    when 'customer.created'
      handle_customer_created
    when 'customer.updated'
      handle_customer_updated
    when 'customer.deleted'
      handle_customer_deleted
    when 'payment_method.attached'
      handle_payment_method_attached
    when 'payment_method.detached'
      handle_payment_method_detached
    else
      Rails.logger.info "Unhandled Stripe webhook event: #{@event.type}"
    end

    render json: { status: 'success' }, status: :ok
  rescue => e
    Rails.logger.error "Stripe webhook error: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    render json: { error: 'Webhook processing failed' }, status: :unprocessable_entity
  end

  private

  def verify_stripe_signature
    payload = request.body.read
    sig_header = request.env['HTTP_STRIPE_SIGNATURE']
    endpoint_secret = Rails.application.credentials.stripe[:webhook_secret] || ENV['STRIPE_WEBHOOK_SECRET']

    begin
      @event = Stripe::Webhook.construct_event(
        payload, sig_header, endpoint_secret
      )
    rescue JSON::ParserError => e
      Rails.logger.error "Invalid JSON in Stripe webhook: #{e.message}"
      render json: { error: 'Invalid JSON' }, status: :bad_request and return
    rescue Stripe::SignatureVerificationError => e
      Rails.logger.error "Invalid Stripe signature: #{e.message}"
      render json: { error: 'Invalid signature' }, status: :bad_request and return
    end
  end

  def handle_subscription_created
    stripe_subscription = @event.data.object
    account = find_account_by_customer_id(stripe_subscription.customer)
    
    return unless account

    subscription = account.subscription || account.build_subscription
    update_subscription_from_stripe(subscription, stripe_subscription)
    
    Rails.logger.info "Subscription created for account #{account.id}: #{stripe_subscription.id}"
  end

  def handle_subscription_updated
    stripe_subscription = @event.data.object
    account = find_account_by_customer_id(stripe_subscription.customer)
    
    return unless account

    subscription = account.subscription
    return unless subscription

    update_subscription_from_stripe(subscription, stripe_subscription)
    
    # Handle plan changes
    if subscription.plan_name_changed?
      account.update!(plan: subscription.plan_name)
      Rails.logger.info "Plan changed for account #{account.id}: #{subscription.plan_name}"
    end
    
    # Handle cancellation
    if stripe_subscription.status == 'canceled'
      account.update!(plan: 'free')
      Rails.logger.info "Subscription canceled for account #{account.id}"
    end
    
    Rails.logger.info "Subscription updated for account #{account.id}: #{stripe_subscription.id}"
  end

  def handle_subscription_deleted
    stripe_subscription = @event.data.object
    account = find_account_by_customer_id(stripe_subscription.customer)
    
    return unless account

    subscription = account.subscription
    return unless subscription

    subscription.update!(
      status: 'canceled',
      canceled_at: Time.current
    )
    
    account.update!(plan: 'free')
    
    Rails.logger.info "Subscription deleted for account #{account.id}: #{stripe_subscription.id}"
  end

  def handle_payment_succeeded
    invoice = @event.data.object
    account = find_account_by_customer_id(invoice.customer)
    
    return unless account

    # Reset usage for new billing period if this is a subscription invoice
    if invoice.subscription.present?
      usage_service = UsageTrackingService.new(account: account)
      usage_service.reset_for_new_period!
      
      Rails.logger.info "Payment succeeded and usage reset for account #{account.id}: #{invoice.id}"
    end
    
    # TODO: Send payment confirmation email
    # TODO: Update any payment-related records
  end

  def handle_payment_failed
    invoice = @event.data.object
    account = find_account_by_customer_id(invoice.customer)
    
    return unless account

    Rails.logger.warn "Payment failed for account #{account.id}: #{invoice.id}"
    
    # TODO: Send payment failure notification
    # TODO: Handle dunning management
    # TODO: Consider downgrading to free plan after multiple failures
  end

  def handle_customer_created
    customer = @event.data.object
    account_id = customer.metadata&.account_id
    
    if account_id.present?
      account = Account.find_by(id: account_id)
      if account && account.stripe_customer_id.blank?
        account.update!(stripe_customer_id: customer.id)
        Rails.logger.info "Customer created and linked to account #{account.id}: #{customer.id}"
      end
    end
  end

  def handle_customer_updated
    customer = @event.data.object
    account = find_account_by_customer_id(customer.id)
    
    return unless account

    # Update any customer-related information if needed
    Rails.logger.info "Customer updated for account #{account.id}: #{customer.id}"
  end

  def handle_customer_deleted
    customer = @event.data.object
    account = find_account_by_customer_id(customer.id)
    
    return unless account

    # Clear the Stripe customer ID and downgrade to free
    account.update!(
      stripe_customer_id: nil,
      plan: 'free'
    )
    
    # Cancel any active subscription
    if account.subscription&.active?
      account.subscription.update!(
        status: 'canceled',
        canceled_at: Time.current
      )
    end
    
    Rails.logger.info "Customer deleted for account #{account.id}: #{customer.id}"
  end

  def handle_payment_method_attached
    payment_method = @event.data.object
    account = find_account_by_customer_id(payment_method.customer)
    
    return unless account

    Rails.logger.info "Payment method attached for account #{account.id}: #{payment_method.id}"
    
    # TODO: Send notification about new payment method
  end

  def handle_payment_method_detached
    payment_method = @event.data.object
    # Note: payment_method.customer might be nil for detached methods
    
    Rails.logger.info "Payment method detached: #{payment_method.id}"
    
    # TODO: Handle any cleanup if needed
  end

  def find_account_by_customer_id(customer_id)
    return nil unless customer_id.present?
    
    Account.find_by(stripe_customer_id: customer_id)
  end

  def update_subscription_from_stripe(subscription, stripe_subscription)
    # Extract plan name from metadata or price ID
    plan_name = stripe_subscription.metadata&.plan_name || 
                extract_plan_from_price_id(stripe_subscription.items.data.first&.price&.id)
    
    subscription.update!(
      plan_name: plan_name,
      status: stripe_subscription.status,
      stripe_subscription_id: stripe_subscription.id,
      stripe_customer_id: stripe_subscription.customer,
      stripe_price_id: stripe_subscription.items.data.first&.price&.id,
      quantity: stripe_subscription.quantity || 1,
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end),
      trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil,
      cancel_at_period_end: stripe_subscription.cancel_at_period_end || false,
      canceled_at: stripe_subscription.canceled_at ? Time.at(stripe_subscription.canceled_at) : nil
    )
  end

  def extract_plan_from_price_id(price_id)
    return 'free' unless price_id.present?
    
    # Find the plan that matches this price ID
    STRIPE_PLANS.each do |plan_name, config|
      return plan_name if config[:stripe_price_id] == price_id
    end
    
    # Default to free if we can't match the price ID
    'free'
  end
end