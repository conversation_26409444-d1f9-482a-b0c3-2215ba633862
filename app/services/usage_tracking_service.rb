# frozen_string_literal: true

class UsageTrackingService
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :account

  def initialize(account:)
    @account = account
  end

  # Check if account can perform an action
  def can_add_contact?
    current_usage = account.current_usage
    return true unless current_usage

    plan_limits = account.plan_config
    current_usage.contacts_count < plan_limits[:contacts]
  end

  def can_send_campaign?
    current_usage = account.current_usage
    return true unless current_usage

    plan_limits = account.plan_config
    current_usage.campaigns_sent_count < plan_limits[:campaigns_per_month]
  end

  def can_create_template?
    current_usage = account.current_usage
    return true unless current_usage

    plan_limits = account.plan_config
    current_usage.templates_count < plan_limits[:templates]
  end

  # Track usage when actions are performed
  def track_contact_added!
    return unless should_track_usage?

    current_usage = ensure_current_usage
    current_usage.increment!(:contacts_count)
    
    # Check if approaching limit
    check_usage_alerts(:contacts)
    
    current_usage
  rescue => e
    Rails.logger.error "Failed to track contact addition for account #{account.id}: #{e.message}"
    nil
  end

  def track_campaign_sent!
    return unless should_track_usage?

    current_usage = ensure_current_usage
    current_usage.increment!(:campaigns_sent_count)
    
    # Check if approaching limit
    check_usage_alerts(:campaigns_per_month)
    
    current_usage
  rescue => e
    Rails.logger.error "Failed to track campaign sent for account #{account.id}: #{e.message}"
    nil
  end

  def track_template_created!
    return unless should_track_usage?

    current_usage = ensure_current_usage
    current_usage.increment!(:templates_count)
    
    # Check if approaching limit
    check_usage_alerts(:templates)
    
    current_usage
  rescue => e
    Rails.logger.error "Failed to track template creation for account #{account.id}: #{e.message}"
    nil
  end

  def track_contact_removed!
    return unless should_track_usage?

    current_usage = ensure_current_usage
    if current_usage.contacts_count > 0
      current_usage.decrement!(:contacts_count)
    end
    
    current_usage
  rescue => e
    Rails.logger.error "Failed to track contact removal for account #{account.id}: #{e.message}"
    nil
  end

  def track_template_removed!
    return unless should_track_usage?

    current_usage = ensure_current_usage
    if current_usage.templates_count > 0
      current_usage.decrement!(:templates_count)
    end
    
    current_usage
  rescue => e
    Rails.logger.error "Failed to track template removal for account #{account.id}: #{e.message}"
    nil
  end

  # Refresh usage from actual data
  def refresh_usage!
    current_usage = ensure_current_usage
    
    # Count actual contacts
    contacts_count = account.contacts.count
    
    # Count campaigns sent this period
    campaigns_sent_count = account.campaigns_sent_this_period
    
    # Count templates
    templates_count = account.email_templates.count
    
    # Calculate storage used (placeholder for now)
    storage_used = calculate_storage_usage
    
    current_usage.update!(
      contacts_count: contacts_count,
      campaigns_sent_count: campaigns_sent_count,
      templates_count: templates_count,
      storage_used: storage_used
    )
    
    # Update last usage check timestamp
    account.update!(last_usage_check: Time.current)
    
    current_usage
  rescue => e
    Rails.logger.error "Failed to refresh usage for account #{account.id}: #{e.message}"
    nil
  end

  # Get usage statistics
  def usage_stats
    current_usage = account.current_usage
    plan_limits = account.plan_config
    
    return default_stats unless current_usage
    
    {
      contacts: {
        used: current_usage.contacts_count,
        limit: plan_limits[:contacts],
        percentage: usage_percentage(current_usage.contacts_count, plan_limits[:contacts])
      },
      campaigns: {
        used: current_usage.campaigns_sent_count,
        limit: plan_limits[:campaigns_per_month],
        percentage: usage_percentage(current_usage.campaigns_sent_count, plan_limits[:campaigns_per_month])
      },
      templates: {
        used: current_usage.templates_count,
        limit: plan_limits[:templates],
        percentage: usage_percentage(current_usage.templates_count, plan_limits[:templates])
      },
      storage: {
        used: current_usage.storage_used,
        limit: plan_limits[:storage] || Float::INFINITY,
        percentage: usage_percentage(current_usage.storage_used, plan_limits[:storage] || Float::INFINITY)
      },
      period: {
        start: current_usage.period_start,
        end: current_usage.period_end,
        days_remaining: current_usage.days_remaining
      }
    }
  end

  # Check if any limits are exceeded
  def over_limits?
    stats = usage_stats
    
    stats[:contacts][:percentage] >= 100 ||
    stats[:campaigns][:percentage] >= 100 ||
    stats[:templates][:percentage] >= 100 ||
    stats[:storage][:percentage] >= 100
  end

  # Check if approaching any limits (80% threshold)
  def approaching_limits?
    stats = usage_stats
    
    stats[:contacts][:percentage] >= 80 ||
    stats[:campaigns][:percentage] >= 80 ||
    stats[:templates][:percentage] >= 80 ||
    stats[:storage][:percentage] >= 80
  end

  # Get limits that are exceeded
  def exceeded_limits
    stats = usage_stats
    exceeded = []
    
    exceeded << :contacts if stats[:contacts][:percentage] >= 100
    exceeded << :campaigns if stats[:campaigns][:percentage] >= 100
    exceeded << :templates if stats[:templates][:percentage] >= 100
    exceeded << :storage if stats[:storage][:percentage] >= 100
    
    exceeded
  end

  # Reset usage for new billing period
  def reset_for_new_period!
    # Archive current usage
    current_usage = account.current_usage
    if current_usage
      current_usage.update!(period_end: Time.current) if current_usage.period_end > Time.current
    end
    
    # Create new usage record
    new_usage = SubscriptionUsage.create_for_current_period(account)
    
    # Update account reset date
    account.update!(usage_reset_date: account.current_period_start)
    
    new_usage
  rescue => e
    Rails.logger.error "Failed to reset usage for account #{account.id}: #{e.message}"
    nil
  end

  private

  def should_track_usage?
    # Don't track usage for enterprise plans (unlimited)
    account.plan != 'enterprise'
  end

  def ensure_current_usage
    account.current_usage || SubscriptionUsage.create_for_current_period(account)
  end

  def usage_percentage(used, limit)
    return 0 if limit == 0 || limit == Float::INFINITY
    ((used.to_f / limit.to_f) * 100).round(2)
  end

  def default_stats
    plan_limits = account.plan_config
    
    {
      contacts: { used: 0, limit: plan_limits[:contacts], percentage: 0 },
      campaigns: { used: 0, limit: plan_limits[:campaigns_per_month], percentage: 0 },
      templates: { used: 0, limit: plan_limits[:templates], percentage: 0 },
      storage: { used: 0, limit: plan_limits[:storage] || Float::INFINITY, percentage: 0 },
      period: { start: nil, end: nil, days_remaining: nil }
    }
  end

  def calculate_storage_usage
    # Placeholder for storage calculation
    # This would calculate actual storage used by the account
    # For now, return 0
    0
  end

  def check_usage_alerts(metric)
    current_usage = account.current_usage
    return unless current_usage

    plan_limits = account.plan_config
    
    case metric
    when :contacts
      percentage = usage_percentage(current_usage.contacts_count, plan_limits[:contacts])
    when :campaigns_per_month
      percentage = usage_percentage(current_usage.campaigns_sent_count, plan_limits[:campaigns_per_month])
    when :templates
      percentage = usage_percentage(current_usage.templates_count, plan_limits[:templates])
    else
      return
    end

    # Send alerts at 80% and 100%
    if percentage >= 100
      send_limit_exceeded_alert(metric)
    elsif percentage >= 80
      send_approaching_limit_alert(metric, percentage)
    end
  end

  def send_limit_exceeded_alert(metric)
    # TODO: Implement email/notification system
    Rails.logger.warn "Account #{account.id} has exceeded #{metric} limit"
  end

  def send_approaching_limit_alert(metric, percentage)
    # TODO: Implement email/notification system
    Rails.logger.info "Account #{account.id} is at #{percentage}% of #{metric} limit"
  end
end