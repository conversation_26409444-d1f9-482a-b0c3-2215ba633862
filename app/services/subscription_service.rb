# frozen_string_literal: true

class SubscriptionService
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :account, :plan_name, :payment_method_id, :trial_days

  def initialize(account:, **options)
    @account = account
    super(options)
  end

  # Create a new subscription
  def create_subscription(plan_name:, payment_method_id: nil, trial_days: nil)
    return failure('Account already has an active subscription') if account.subscription&.active?
    return failure('Invalid plan') unless valid_plan?(plan_name)
    return success_free_plan(plan_name) if plan_name == 'free'

    # Ensure we have a Stripe customer
    customer_id = account.ensure_stripe_customer!
    return failure('Failed to create Stripe customer') unless customer_id

    # Attach payment method if provided
    if payment_method_id.present?
      attach_payment_method(customer_id, payment_method_id)
    end

    # Create Stripe subscription
    stripe_subscription = create_stripe_subscription(customer_id, plan_name, trial_days)
    return failure('Failed to create Stripe subscription') unless stripe_subscription

    # Create or update local subscription
    subscription = create_local_subscription(stripe_subscription, plan_name)
    return failure('Failed to create local subscription') unless subscription

    # Update account plan
    account.update!(plan: plan_name)

    success(subscription)
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating subscription: #{e.message}"
    failure("Payment error: #{e.user_message || e.message}")
  rescue => e
    Rails.logger.error "Error creating subscription: #{e.message}"
    failure('An unexpected error occurred')
  end

  # Change subscription plan
  def change_plan(new_plan_name:)
    return failure('Invalid plan') unless valid_plan?(new_plan_name)
    return downgrade_to_free(new_plan_name) if new_plan_name == 'free'
    
    subscription = account.subscription
    return failure('No active subscription found') unless subscription&.active?
    return success(subscription) if subscription.plan_name == new_plan_name

    # Update Stripe subscription
    stripe_subscription = update_stripe_subscription(subscription, new_plan_name)
    return failure('Failed to update Stripe subscription') unless stripe_subscription

    # Update local subscription and account
    subscription.update!(
      plan_name: new_plan_name,
      stripe_price_id: stripe_subscription.items.data.first&.price&.id
    )
    account.update!(plan: new_plan_name)

    success(subscription)
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error changing plan: #{e.message}"
    failure("Payment error: #{e.user_message || e.message}")
  rescue => e
    Rails.logger.error "Error changing plan: #{e.message}"
    failure('An unexpected error occurred')
  end

  # Cancel subscription
  def cancel_subscription(immediate: false)
    subscription = account.subscription
    return failure('No active subscription found') unless subscription&.active?

    if immediate
      cancel_immediately(subscription)
    else
      cancel_at_period_end(subscription)
    end
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error canceling subscription: #{e.message}"
    failure("Payment error: #{e.user_message || e.message}")
  rescue => e
    Rails.logger.error "Error canceling subscription: #{e.message}"
    failure('An unexpected error occurred')
  end

  # Reactivate subscription
  def reactivate_subscription
    subscription = account.subscription
    return failure('No subscription found') unless subscription
    return failure('Subscription is not scheduled for cancellation') unless subscription.cancel_at_period_end?

    success = subscription.reactivate!
    return failure('Failed to reactivate subscription') unless success

    success(subscription)
  rescue => e
    Rails.logger.error "Error reactivating subscription: #{e.message}"
    failure('An unexpected error occurred')
  end

  # Update payment method
  def update_payment_method(payment_method_id:)
    return failure('Payment method ID is required') if payment_method_id.blank?
    return failure('No Stripe customer found') unless account.stripe_customer_id.present?

    # Attach the new payment method
    payment_method = Stripe::PaymentMethod.attach(
      payment_method_id,
      { customer: account.stripe_customer_id }
    )

    # Set as default payment method
    Stripe::Customer.update(
      account.stripe_customer_id,
      {
        invoice_settings: {
          default_payment_method: payment_method_id
        }
      }
    )

    success(payment_method)
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error updating payment method: #{e.message}"
    failure("Payment error: #{e.user_message || e.message}")
  rescue => e
    Rails.logger.error "Error updating payment method: #{e.message}"
    failure('An unexpected error occurred')
  end

  private

  def valid_plan?(plan_name)
    STRIPE_PLANS.key?(plan_name)
  end

  def success_free_plan(plan_name)
    # Handle free plan (no Stripe subscription needed)
    subscription = account.subscription || account.build_subscription
    subscription.update!(
      plan_name: plan_name,
      status: 'active',
      stripe_subscription_id: nil,
      stripe_customer_id: nil,
      stripe_price_id: nil
    )
    account.update!(plan: plan_name)
    success(subscription)
  end

  def attach_payment_method(customer_id, payment_method_id)
    Stripe::PaymentMethod.attach(
      payment_method_id,
      { customer: customer_id }
    )

    # Set as default payment method
    Stripe::Customer.update(
      customer_id,
      {
        invoice_settings: {
          default_payment_method: payment_method_id
        }
      }
    )
  end

  def create_stripe_subscription(customer_id, plan_name, trial_days)
    plan_config = STRIPE_PLANS[plan_name]
    return nil unless plan_config[:stripe_price_id]

    subscription_params = {
      customer: customer_id,
      items: [{ price: plan_config[:stripe_price_id] }],
      metadata: {
        account_id: account.id,
        plan_name: plan_name
      }
    }

    if trial_days.present? && trial_days > 0
      subscription_params[:trial_period_days] = trial_days
    end

    Stripe::Subscription.create(subscription_params)
  end

  def create_local_subscription(stripe_subscription, plan_name)
    subscription = account.subscription || account.build_subscription
    subscription.update!(
      plan_name: plan_name,
      status: stripe_subscription.status,
      stripe_subscription_id: stripe_subscription.id,
      stripe_customer_id: stripe_subscription.customer,
      stripe_price_id: stripe_subscription.items.data.first&.price&.id,
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end),
      trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil
    )
    subscription
  end

  def update_stripe_subscription(subscription, new_plan_name)
    plan_config = STRIPE_PLANS[new_plan_name]
    return nil unless plan_config[:stripe_price_id]

    Stripe::Subscription.update(
      subscription.stripe_subscription_id,
      {
        items: [{
          id: subscription.stripe_subscription_id,
          price: plan_config[:stripe_price_id]
        }],
        metadata: {
          account_id: account.id,
          plan_name: new_plan_name
        }
      }
    )
  end

  def downgrade_to_free(plan_name)
    subscription = account.subscription
    return success_free_plan(plan_name) unless subscription&.stripe_subscription_id

    # Cancel the Stripe subscription
    Stripe::Subscription.delete(subscription.stripe_subscription_id)

    # Update local subscription
    subscription.update!(
      plan_name: plan_name,
      status: 'canceled',
      stripe_subscription_id: nil,
      stripe_price_id: nil,
      canceled_at: Time.current
    )
    account.update!(plan: plan_name)

    success(subscription)
  end

  def cancel_immediately(subscription)
    if subscription.stripe_subscription_id.present?
      Stripe::Subscription.delete(subscription.stripe_subscription_id)
    end

    subscription.update!(
      status: 'canceled',
      canceled_at: Time.current
    )
    account.update!(plan: 'free')

    success(subscription)
  end

  def cancel_at_period_end(subscription)
    success = subscription.cancel_at_period_end!
    return failure('Failed to schedule cancellation') unless success

    success(subscription)
  end

  def success(data = nil)
    OpenStruct.new(success?: true, data: data, error: nil)
  end

  def failure(error_message)
    OpenStruct.new(success?: false, data: nil, error: error_message)
  end
end