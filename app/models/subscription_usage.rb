# frozen_string_literal: true

class SubscriptionUsage < ApplicationRecord
  # Associations
  belongs_to :account

  # Validations
  validates :period_start, presence: true
  validates :period_end, presence: true
  validates :contacts_count, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :campaigns_sent_count, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :templates_count, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :storage_used, presence: true, numericality: { greater_than_or_equal_to: 0 }
  
  validate :period_end_after_period_start
  validate :unique_period_per_account

  # Scopes
  scope :current_period, -> { where('period_start <= ? AND period_end >= ?', Time.current, Time.current) }
  scope :for_account, ->(account) { where(account: account) }
  scope :for_period, ->(start_date, end_date) { where(period_start: start_date..end_date) }

  # Class methods
  def self.current_for_account(account)
    current_period.find_by(account: account)
  end

  def self.create_for_current_period(account)
    period_start = account.current_period_start
    period_end = account.current_period_end
    
    create!(
      account: account,
      period_start: period_start,
      period_end: period_end,
      contacts_count: account.contacts.count,
      campaigns_sent_count: account.campaigns_sent_this_period.count,
      templates_count: account.templates.count,
      storage_used: 0 # TODO: Calculate actual storage usage
    )
  end

  # Instance methods
  def current_period?
    period_start <= Time.current && period_end >= Time.current
  end

  def expired?
    period_end < Time.current
  end

  def days_remaining
    return 0 if expired?
    
    ((period_end - Time.current) / 1.day).ceil
  end

  def usage_percentage_for(metric)
    return 0 unless account.subscription&.active?
    
    current_usage = send("#{metric}_count")
    limit = account.plan_limits[metric.to_s.gsub('_count', '').to_sym]
    
    return 0 if limit.nil? || limit == Float::INFINITY
    
    (current_usage.to_f / limit * 100).round(1)
  end

  def approaching_limit?(metric, threshold = 80)
    usage_percentage_for(metric) >= threshold
  end

  def over_limit?(metric)
    return false unless account.subscription&.active?
    
    current_usage = send("#{metric}_count")
    limit = account.plan_limits[metric.to_s.gsub('_count', '').to_sym]
    
    return false if limit.nil? || limit == Float::INFINITY
    
    current_usage > limit
  end

  def refresh_usage!
    update!(
      contacts_count: account.contacts.count,
      campaigns_sent_count: account.campaigns_sent_this_period.count,
      templates_count: account.templates.count,
      storage_used: calculate_storage_usage
    )
  end

  private

  def period_end_after_period_start
    return unless period_start && period_end
    
    errors.add(:period_end, 'must be after period start') if period_end <= period_start
  end

  def unique_period_per_account
    return unless account && period_start
    
    existing = self.class.where(account: account, period_start: period_start)
    existing = existing.where.not(id: id) if persisted?
    
    errors.add(:period_start, 'already exists for this account') if existing.exists?
  end

  def calculate_storage_usage
    # TODO: Implement actual storage calculation
    # This could include:
    # - Uploaded images/files
    # - Email templates storage
    # - Contact data storage
    0
  end
 end