class Account < ApplicationRecord
  # Associations
  has_many :users, dependent: :destroy
  has_one :subscription, dependent: :destroy
  has_many :campaigns, dependent: :destroy
  has_many :contacts, dependent: :destroy
  has_many :templates, dependent: :destroy
  has_many :tags, dependent: :destroy
  has_many :brand_voices, dependent: :destroy
  has_many :subscription_usages, dependent: :destroy
  has_many :email_automations, dependent: :destroy

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :subdomain, presence: true, uniqueness: { case_sensitive: false },
                       format: { with: /\A[a-z0-9\-]+\z/, message: "can only contain lowercase letters, numbers, and hyphens" },
                       length: { minimum: 3, maximum: 30 }
  validates :plan, inclusion: { in: %w[free starter professional enterprise] }
  validates :status, inclusion: { in: %w[active suspended cancelled] }

  # Callbacks
  before_validation :normalize_subdomain

  # Scopes
  scope :active, -> { where(status: "active") }
  scope :by_plan, ->(plan) { where(plan: plan) }

  # Methods
  def owner
    users.find_by(role: "owner")
  end

  def active?
    status == "active"
  end

  def suspended?
    status == "suspended"
  end

  def cancelled?
    status == "cancelled"
  end

  def plan_limits
    case plan
    when "free"
      { contacts: 100, campaigns_per_month: 5, templates: 3 }
    when "starter"
      { contacts: 1000, campaigns_per_month: 50, templates: 10 }
    when "professional"
      { contacts: 10000, campaigns_per_month: 200, templates: 50 }
    when "enterprise"
      { contacts: Float::INFINITY, campaigns_per_month: Float::INFINITY, templates: Float::INFINITY }
    else
      { contacts: 100, campaigns_per_month: 5, templates: 3 } # Default to free
    end
  end

  # Stripe Customer Management
  def ensure_stripe_customer!
    return stripe_customer_id if stripe_customer_id.present?

    customer = Stripe::Customer.create(
      email: owner&.email,
      name: name,
      metadata: {
        account_id: id,
        subdomain: subdomain
      }
    )

    update!(stripe_customer_id: customer.id)
    customer.id
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to create Stripe customer for account #{id}: #{e.message}"
    nil
  end

  def stripe_customer
    return nil unless stripe_customer_id.present?
    
    @stripe_customer ||= Stripe::Customer.retrieve(stripe_customer_id)
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to retrieve Stripe customer #{stripe_customer_id}: #{e.message}"
    nil
  end

  # Usage Tracking
  def current_usage
    @current_usage ||= subscription_usages.current_for_account(self) || create_current_usage_record
  end

  def refresh_usage!
    current_usage&.refresh_usage!
    @current_usage = nil # Clear memoization
    current_usage
  end

  def usage_percentage_for(metric)
    current_usage&.usage_percentage_for(metric) || 0
  end

  def approaching_limit?(metric, threshold = 80)
    current_usage&.approaching_limit?(metric, threshold) || false
  end

  def over_limit?(metric)
    current_usage&.over_limit?(metric) || false
  end

  def can_add_contact?
    !over_limit?(:contacts)
  end

  def can_send_campaign?
    !over_limit?(:campaigns_sent)
  end

  def can_create_template?
    !over_limit?(:templates)
  end

  # Billing Period Management
  def current_period_start
    subscription&.current_period_start || usage_reset_date || created_at.beginning_of_month
  end

  def current_period_end
    subscription&.current_period_end || (current_period_start + 1.month)
  end

  def days_until_reset
    return 0 if current_period_end < Time.current
    
    ((current_period_end - Time.current) / 1.day).ceil
  end

  # Campaign tracking for current period
  def campaigns_sent_this_period
    campaigns.where(sent_at: current_period_start..current_period_end)
  end

  # Plan management
  def can_upgrade_to?(target_plan)
    subscription&.can_upgrade_to?(target_plan) || (plan != target_plan && target_plan != 'free')
  end

  def can_downgrade_to?(target_plan)
    subscription&.can_downgrade_to?(target_plan) || (plan != target_plan)
  end

  def plan_config
    STRIPE_PLANS[plan] || STRIPE_PLANS['free']
  end

  private

  def create_current_usage_record
     SubscriptionUsage.create_for_current_period(self)
   rescue => e
     Rails.logger.error "Failed to create usage record for account #{id}: #{e.message}"
     nil
   end

  private

  def normalize_subdomain
    self.subdomain = subdomain&.downcase&.strip
  end
end
