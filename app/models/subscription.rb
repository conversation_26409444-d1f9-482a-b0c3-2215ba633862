class Subscription < ApplicationRecord
  # Associations
  belongs_to :account
  has_many :subscription_usages, through: :account

  # Validations
  validates :plan_name, presence: true
  validates :status, inclusion: { in: %w[active past_due canceled unpaid trialing incomplete incomplete_expired] }
  validates :stripe_subscription_id, presence: true, uniqueness: true, allow_blank: true
  validates :stripe_customer_id, presence: true, allow_blank: true
  validates :quantity, presence: true, numericality: { greater_than: 0 }
  
  # Callbacks
  before_validation :set_defaults
  after_create :create_initial_usage_record

  # Scopes
  scope :active, -> { where(status: "active") }
  scope :trialing, -> { where(status: "trialing") }
  scope :past_due, -> { where(status: "past_due") }
  scope :canceled, -> { where(status: "canceled") }

  # Methods
  def active?
    status == "active"
  end

  def trialing?
    status == "trialing"
  end

  def past_due?
    status == "past_due"
  end

  def canceled?
    status == "canceled"
  end

  def on_trial?
    trial_end.present? && trial_end > Time.current
  end

  def trial_days_remaining
    return 0 unless on_trial?

    ((trial_end - Time.current) / 1.day).ceil
  end

  def current_period_days_remaining
    return 0 unless current_period_end.present?

    days = ((current_period_end - Time.current) / 1.day).ceil
    [ days, 0 ].max
  end

  def sync_with_stripe!
    return unless stripe_subscription_id.present?

    stripe_subscription = Stripe::Subscription.retrieve(stripe_subscription_id)

    update!(
      status: stripe_subscription.status,
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end),
      trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil,
      stripe_price_id: stripe_subscription.items.data.first&.price&.id,
      quantity: stripe_subscription.quantity || 1
    )
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to sync subscription #{id} with Stripe: #{e.message}"
    false
  end

  def free_plan?
    plan_name == 'free'
  end

  def paid_plan?
    !free_plan?
  end

  def can_downgrade_to?(target_plan)
    return false if target_plan == plan_name
    return true if target_plan == 'free'
    
    current_plan_index = STRIPE_PLANS.keys.index(plan_name)
    target_plan_index = STRIPE_PLANS.keys.index(target_plan)
    
    return false unless current_plan_index && target_plan_index
    
    target_plan_index < current_plan_index
  end

  def can_upgrade_to?(target_plan)
    return false if target_plan == plan_name
    return false if target_plan == 'free'
    
    current_plan_index = STRIPE_PLANS.keys.index(plan_name)
    target_plan_index = STRIPE_PLANS.keys.index(target_plan)
    
    return false unless current_plan_index && target_plan_index
    
    target_plan_index > current_plan_index
  end

  def plan_config
    STRIPE_PLANS[plan_name] || STRIPE_PLANS['free']
  end

  def monthly_price
    plan_config[:price]
  end

  def monthly_price_in_dollars
    monthly_price / 100.0
  end

  def cancel_at_period_end!
    return false unless stripe_subscription_id.present?

    stripe_subscription = Stripe::Subscription.update(
      stripe_subscription_id,
      { cancel_at_period_end: true }
    )

    update!(
      cancel_at_period_end: true
    )

    true
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to cancel subscription #{id}: #{e.message}"
    false
  end

  def reactivate!
    return false unless stripe_subscription_id.present?
    return false unless cancel_at_period_end?

    stripe_subscription = Stripe::Subscription.update(
      stripe_subscription_id,
      { cancel_at_period_end: false }
    )

    update!(
      cancel_at_period_end: false,
      canceled_at: nil
    )

    true
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to reactivate subscription #{id}: #{e.message}"
    false
  end

  def current_usage
    account.current_usage
  end

  private

  def set_defaults
    self.quantity ||= 1
    self.status ||= 'active'
  end

  def create_initial_usage_record
    return if account.subscription_usages.current_period.exists?
    
    SubscriptionUsage.create_for_current_period(account)
  rescue => e
    Rails.logger.error "Failed to create initial usage record for account #{account.id}: #{e.message}"
  end
end
