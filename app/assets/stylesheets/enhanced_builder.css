/* Enhanced Template Builder Styles with TikTok and Social Media Support */

:root {
  /* Brand Colors */
  --tiktok-black: #000000;
  --tiktok-red: #ff0050;
  --tiktok-blue: #25f4ee;
  --instagram-gradient: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  --youtube-red: #ff0000;
  --linkedin-blue: #0077b5;
  --facebook-blue: #1877f2;
  
  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}

/* Canvas Component Base Styles */
.canvas-component {
  position: relative;
  margin: 10px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: move;
}

.canvas-component:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.canvas-component.selected {
  outline: 3px solid #667eea;
  outline-offset: 2px;
}

/* TikTok Styles */
.platform-tiktok {
  background: var(--tiktok-black);
  color: white;
  aspect-ratio: 9/16;
  max-width: 300px;
  position: relative;
}

.tiktok-video-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  position: relative;
  overflow: hidden;
}

.tiktok-video-frame {
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23000"/><circle cx="50" cy="50" r="20" fill="%23ff0050"/></svg>') center/cover;
  position: relative;
}

.tiktok-overlay {
  position: absolute;
  bottom: 80px;
  left: 20px;
  right: 20px;
  z-index: 2;
}

.tiktok-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.tiktok-description {
  font-size: 14px;
  line-height: 1.4;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.tiktok-effects {
  position: absolute;
  right: 15px;
  bottom: 100px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.effect-sparkle, .effect-heart {
  font-size: 24px;
  animation: pulse 2s infinite;
}

.tiktok-text-overlay {
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  text-align: center;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.animated-text {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.text-line {
  font-size: 24px;
  font-weight: bold;
  color: var(--tiktok-red);
  animation: slideIn 0.5s ease forwards;
  opacity: 0;
  transform: translateX(-50px);
}

.text-line:nth-child(1) { animation-delay: 0.2s; }
.text-line:nth-child(2) { animation-delay: 0.4s; }
.text-line:nth-child(3) { animation-delay: 0.6s; }

.tiktok-hashtag-challenge {
  background: linear-gradient(135deg, var(--tiktok-black) 0%, var(--tiktok-red) 100%);
  padding: 30px 20px;
  text-align: center;
  color: white;
}

.challenge-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 15px;
  color: var(--tiktok-blue);
}

.challenge-description {
  font-size: 16px;
  margin-bottom: 20px;
  line-height: 1.5;
}

.hashtag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.hashtag {
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

/* Instagram Styles */
.platform-instagram {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.instagram-story {
  aspect-ratio: 9/16;
  background: var(--instagram-gradient);
  color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.story-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.profile-pic {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.username {
  font-weight: 600;
  font-size: 14px;
}

.story-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.story-content h2 {
  font-size: 24px;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.story-cta {
  text-align: center;
}

.story-button {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
}

.instagram-post {
  aspect-ratio: 1/1;
  background: white;
}

.post-image {
  width: 100%;
  height: 70%;
  background: var(--instagram-gradient);
}

.post-content {
  padding: 15px;
  font-size: 14px;
  line-height: 1.4;
}

.instagram-reel {
  aspect-ratio: 9/16;
  background: #000;
  color: white;
  position: relative;
}

.reel-video {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
}

.reel-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
}

/* YouTube Styles */
.platform-youtube {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.youtube-shorts {
  aspect-ratio: 9/16;
  background: #000;
  color: white;
  position: relative;
}

.shorts-video {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--youtube-red) 0%, #cc0000 100%);
}

.shorts-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
}

.youtube-thumbnail {
  aspect-ratio: 16/9;
  background: #000;
  position: relative;
  cursor: pointer;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--youtube-red) 0%, #ff4444 100%);
}

.thumbnail-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.thumbnail-overlay h2 {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.play-button {
  font-size: 48px;
  opacity: 0.9;
  transition: transform 0.3s ease;
}

.youtube-thumbnail:hover .play-button {
  transform: scale(1.1);
}

/* LinkedIn Styles */
.platform-linkedin {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.linkedin-post {
  padding: 20px;
}

.post-header {
  margin-bottom: 15px;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-info .profile-pic {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--linkedin-blue);
}

.profile-details h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #000;
}

.profile-details p {
  margin: 2px 0 0 0;
  font-size: 14px;
  color: #666;
}

.post-content {
  font-size: 14px;
  line-height: 1.5;
  color: #000;
}

.linkedin-article {
  padding: 30px;
  background: white;
  border-radius: 8px;
}

.linkedin-article h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #000;
}

.article-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  font-size: 14px;
  color: #666;
}

.article-content {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

/* Universal Social Components */
.social-carousel {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.carousel-slide {
  padding: 40px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: none;
}

.carousel-slide.active {
  display: block;
}

.carousel-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
}

.indicator.active {
  background: white;
}

.social-quote {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 40px;
  text-align: center;
  border-radius: 12px;
}

.social-quote blockquote {
  font-size: 24px;
  font-style: italic;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.social-quote cite {
  font-size: 16px;
  font-weight: 600;
  opacity: 0.9;
}

/* Email Styles */
.platform-email {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.email-header {
  background: #f9fafb;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #1f2937;
}

.email-nav {
  display: flex;
  gap: 20px;
}

.email-nav a {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
}

.email-cta {
  padding: 30px;
  text-align: center;
}

.cta-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
}

/* Animations */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes slideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .canvas-component {
    margin: 5px;
  }
  
  .tiktok-title {
    font-size: 16px;
  }
  
  .tiktok-description {
    font-size: 12px;
  }
  
  .challenge-title {
    font-size: 24px;
  }
  
  .story-content h2 {
    font-size: 20px;
  }
  
  .thumbnail-overlay h2 {
    font-size: 16px;
  }
  
  .play-button {
    font-size: 36px;
  }
}

/* Drag and Drop States */
.drag-over {
  background: #f0f4ff !important;
  border-color: #667eea !important;
  transform: scale(1.02);
}

.component-card[draggable="true"] {
  cursor: grab;
}

.component-card[draggable="true"]:active {
  cursor: grabbing;
}

/* Platform-specific hover effects */
.platform-tiktok:hover {
  box-shadow: 0 10px 25px rgba(255, 0, 80, 0.3);
}

.platform-instagram:hover {
  box-shadow: 0 10px 25px rgba(188, 24, 136, 0.3);
}

.platform-youtube:hover {
  box-shadow: 0 10px 25px rgba(255, 0, 0, 0.3);
}

.platform-linkedin:hover {
  box-shadow: 0 10px 25px rgba(0, 119, 181, 0.3);
}

.platform-email:hover {
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}
