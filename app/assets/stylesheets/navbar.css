/* Navbar Styles */
.navbar-dropdown {
  position: relative;
}

.navbar-dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 0.5rem;
  width: 12rem;
  border-radius: 0.375rem;
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 9999;
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
}

.navbar-dropdown-menu.hidden {
  opacity: 0;
  transform: scale(0.95);
  pointer-events: none;
}

.navbar-dropdown-menu a {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.15s ease-in-out;
}

.navbar-dropdown-menu a:hover {
  background-color: #f3f4f6;
}

.navbar-dropdown-menu hr {
  margin: 0.25rem 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

/* User Avatar */
.user-avatar {
  height: 2rem;
  width: 2rem;
  border-radius: 50%;
  background-color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.user-avatar:hover {
  background-color: #4f46e5;
  transform: scale(1.05);
}

.user-avatar:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.user-avatar span {
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
}

/* Mobile Menu Improvements */
.mobile-menu-button {
  background-color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: #9ca3af;
  transition: all 0.15s ease-in-out;
}

.mobile-menu-button:hover {
  color: #6b7280;
  background-color: #f9fafb;
}

.mobile-menu-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1;
}

/* Ensure dropdown appears above other content */
.relative {
  position: relative;
}

/* Fix for potential z-index conflicts */
nav {
  position: relative;
  z-index: 1000;
}
