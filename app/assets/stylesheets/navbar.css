/* Enhanced Navbar Styles */

/* Main Navigation */
nav {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Logo hover effect */
.logo-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-link:hover {
  transform: scale(1.02);
}

/* Navigation Links */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.nav-link:hover::before {
  width: 100%;
}

.nav-link.active::before {
  width: 100%;
}

/* Dropdown Animations */
.navbar-dropdown {
  position: relative;
}

.navbar-dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 0.5rem;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 9999;
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.navbar-dropdown-menu.hidden {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
  pointer-events: none;
}

.navbar-dropdown-menu a,
.navbar-dropdown-menu button {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: #374151;
  text-decoration: none;
  transition: all 0.15s ease-in-out;
  border-radius: 0.5rem;
  margin: 0.125rem 0.5rem;
}

.navbar-dropdown-menu a:hover,
.navbar-dropdown-menu button:hover {
  background-color: #f3f4f6;
  color: #1f2937;
  transform: translateX(4px);
}

.navbar-dropdown-menu a:first-child {
  margin-top: 0.5rem;
}

.navbar-dropdown-menu a:last-child {
  margin-bottom: 0.5rem;
}

.navbar-dropdown-menu hr {
  margin: 0.5rem 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

/* User Avatar */
.user-avatar {
  height: 2rem;
  width: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.user-avatar:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: scale(1.1);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.user-avatar:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.user-avatar span {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

/* Enhanced User Menu Button */
.user-menu-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.75rem;
}

.user-menu-button:hover {
  background-color: #f9fafb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Notification Badge */
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Mobile Menu Improvements */
.mobile-menu-button {
  background-color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: #9ca3af;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.mobile-menu-button:hover {
  color: #6b7280;
  background-color: #f9fafb;
  transform: scale(1.05);
}

.mobile-menu-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6366f1;
}

/* Mobile Menu Animation */
#mobile-menu {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

#mobile-menu.hidden {
  transform: scaleY(0);
  opacity: 0;
}

#mobile-menu:not(.hidden) {
  transform: scaleY(1);
  opacity: 1;
}

/* Mobile Navigation Links */
.mobile-nav-link {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.5rem;
  margin: 0.125rem 0.5rem;
}

.mobile-nav-link:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mobile-nav-link.active {
  background: linear-gradient(135deg, #ede9fe, #ddd6fe);
  border-left: 4px solid #6366f1;
}

/* Quick Action Buttons */
.quick-action-btn {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-action-btn:hover {
  background-color: #f3f4f6;
  transform: scale(1.02);
}

/* Sticky Navigation */
.sticky-nav {
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sticky-nav.scrolled {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Responsive Design Enhancements */
@media (max-width: 640px) {
  .navbar-dropdown-menu {
    width: calc(100vw - 2rem);
    right: 1rem;
    left: 1rem;
  }
}

/* Dark Mode Support (if needed) */
@media (prefers-color-scheme: dark) {
  .navbar-dropdown-menu {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .navbar-dropdown-menu a,
  .navbar-dropdown-menu button {
    color: #d1d5db;
  }
  
  .navbar-dropdown-menu a:hover,
  .navbar-dropdown-menu button:hover {
    background-color: #374151;
    color: #f9fafb;
  }
}

/* Loading States */
.nav-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.mobile-nav-link:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Icon Animations */
.nav-icon {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover .nav-icon {
  transform: scale(1.1);
}

/* Gradient Text Effect */
.gradient-text {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Ensure dropdown appears above other content */
.relative {
  position: relative;
}

/* Fix for potential z-index conflicts */
nav {
  position: relative;
  z-index: 1000;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for dropdown menus */
.navbar-dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.navbar-dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.navbar-dropdown-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.navbar-dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
