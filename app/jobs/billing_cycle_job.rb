# Job for handling periodic billing cycle operations
class BillingCycleJob < ApplicationJob
  queue_as :default
  
  # This job should be scheduled to run daily to handle billing cycle operations
  def perform
    Rails.logger.info "Starting billing cycle job"
    
    # Process all accounts with active subscriptions
    process_billing_reminders
    process_usage_resets
    process_subscription_syncs
    sync_failed_payments
    cleanup_old_usage_records
    
    Rails.logger.info "Billing cycle job completed"
  rescue => e
    Rails.logger.error "Error in BillingCycleJob: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end
  
  private
  
  def process_billing_reminders
    Rails.logger.info "Processing billing reminders"
    
    # Get reminder days from configuration
    reminder_days = BILLING_CONFIG['invoice_reminder_days'] || [7, 3, 1]
    
    reminder_days.each do |days|
      # Find accounts with billing dates approaching
      accounts_to_remind = Account.joins(:subscription)
                                 .where(subscriptions: { status: 'active' })
                                 .where(
                                   "subscriptions.current_period_end::date = ?",
                                   Date.current + days.days
                                 )
      
      accounts_to_remind.find_each do |account|
        Rails.logger.info "Sending billing reminder to account #{account.id} (#{days} days)"
        
        UsageTrackingJob.perform_later(
          'send_usage_alert',
          account.id,
          {
            alert_type: 'billing_reminder',
            days_until_billing: days
          }
        )
      end
    end
  end
  
  def process_usage_resets
    Rails.logger.info "Processing usage resets for new billing periods"
    
    # Find accounts whose billing period started today
    accounts_for_reset = Account.joins(:subscription)
                               .where(subscriptions: { status: 'active' })
                               .where(
                                 "subscriptions.current_period_start::date = ?",
                                 Date.current
                               )
    
    accounts_for_reset.find_each do |account|
      Rails.logger.info "Resetting usage for account #{account.id} (new billing period)"
      
      UsageTrackingJob.perform_later('reset_usage', account.id)
    end
  end
  
  def process_subscription_syncs
    Rails.logger.info "Processing subscription syncs with Stripe"
    
    # Sync subscriptions that haven't been synced recently
    stale_subscriptions = Subscription.where(status: 'active')
                                    .where(stripe_subscription_id: nil)
                                    .or(
                                      Subscription.where(
                                        "updated_at < ?",
                                        1.day.ago
                                      )
                                    )
                                    .limit(50) # Limit to avoid rate limits
    
    stale_subscriptions.find_each do |subscription|
      next unless subscription.account
      
      Rails.logger.info "Syncing subscription for account #{subscription.account.id}"
      
      UsageTrackingJob.perform_later('sync_subscription', subscription.account.id)
    end
  end
  
  def sync_failed_payments
    Rails.logger.info "Processing accounts with failed payments"
    
    # Find accounts with recent payment failures
    failed_payment_accounts = Account.joins(:subscription)
                                    .where.not(subscriptions: { payment_failed_at: nil })
                                    .where(
                                      "subscriptions.payment_failed_at > ?",
                                      7.days.ago
                                    )
    
    failed_payment_accounts.find_each do |account|
      days_since_failure = (Time.current - account.subscription.payment_failed_at) / 1.day
      
      case days_since_failure.to_i
      when 1
        send_payment_retry_reminder(account)
      when 3
        send_final_payment_notice(account)
      when 7
        suspend_account_for_non_payment(account)
      end
    end
  end
  
  def cleanup_old_usage_records
    Rails.logger.info "Cleaning up old usage records"
    
    # Keep usage records for the last 12 months
    cutoff_date = 12.months.ago
    
    old_records_count = SubscriptionUsage.where("period_start < ?", cutoff_date).count
    
    if old_records_count > 0
      Rails.logger.info "Deleting #{old_records_count} old usage records"
      SubscriptionUsage.where("period_start < ?", cutoff_date).delete_all
    end
  end
  
  def send_payment_retry_reminder(account)
    Rails.logger.info "Sending payment retry reminder to account #{account.id}"
    
    # TODO: Implement email notification
    # UserMailer.payment_retry_reminder(account).deliver_now
  end
  
  def send_final_payment_notice(account)
    Rails.logger.info "Sending final payment notice to account #{account.id}"
    
    # TODO: Implement email notification
    # UserMailer.final_payment_notice(account).deliver_now
  end
  
  def suspend_account_for_non_payment(account)
    Rails.logger.warn "Suspending account #{account.id} for non-payment"
    
    # Downgrade to free plan
    if account.subscription
      account.subscription.update!(
        plan: 'free',
        status: 'past_due'
      )
    end
    
    # Reset usage to free plan limits
    UsageTrackingJob.perform_later('reset_usage', account.id)
    
    # TODO: Implement email notification
    # UserMailer.account_suspended(account).deliver_now
    
    # TODO: Implement account suspension logic
    # account.update!(suspended: true, suspended_at: Time.current)
  end
end