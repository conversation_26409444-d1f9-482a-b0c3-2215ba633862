# Job for processing Stripe webhooks in the background
class StripeWebhookJob < ApplicationJob
  queue_as :default
  
  def perform(event_data)
    event_type = event_data['type']
    event_object = event_data['data']['object']
    
    Rails.logger.info "Processing Stripe webhook: #{event_type}"
    
    case event_type
    when 'customer.subscription.created'
      handle_subscription_created(event_object)
    when 'customer.subscription.updated'
      handle_subscription_updated(event_object)
    when 'customer.subscription.deleted'
      handle_subscription_deleted(event_object)
    when 'invoice.payment_succeeded'
      handle_payment_succeeded(event_object)
    when 'invoice.payment_failed'
      handle_payment_failed(event_object)
    when 'customer.created'
      handle_customer_created(event_object)
    when 'customer.updated'
      handle_customer_updated(event_object)
    when 'customer.deleted'
      handle_customer_deleted(event_object)
    when 'payment_method.attached'
      handle_payment_method_attached(event_object)
    when 'payment_method.detached'
      handle_payment_method_detached(event_object)
    else
      Rails.logger.info "Unhandled webhook event type: #{event_type}"
    end
  rescue => e
    Rails.logger.error "Error processing Stripe webhook #{event_type}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end
  
  private
  
  def handle_subscription_created(subscription)
    customer_id = subscription['customer']
    account = find_account_by_stripe_customer_id(customer_id)
    return unless account
    
    Rails.logger.info "Creating subscription for account #{account.id}"
    
    # Create or update subscription
    subscription_record = account.subscription || account.build_subscription
    
    subscription_record.update!(
      stripe_subscription_id: subscription['id'],
      stripe_customer_id: customer_id,
      stripe_price_id: subscription['items']['data'].first['price']['id'],
      status: subscription['status'],
      current_period_start: Time.at(subscription['current_period_start']),
      current_period_end: Time.at(subscription['current_period_end']),
      quantity: subscription['quantity'] || 1
    )
    
    # Determine plan from price ID
    plan_name = determine_plan_from_price_id(subscription_record.stripe_price_id)
    subscription_record.update!(plan: plan_name) if plan_name
    
    # Create initial usage record
    subscription_record.create_initial_usage_record
    
    Rails.logger.info "Subscription created for account #{account.id}"
  end
  
  def handle_subscription_updated(subscription)
    subscription_record = find_subscription_by_stripe_id(subscription['id'])
    return unless subscription_record
    
    Rails.logger.info "Updating subscription #{subscription_record.id}"
    
    # Update subscription details
    subscription_record.update!(
      status: subscription['status'],
      current_period_start: Time.at(subscription['current_period_start']),
      current_period_end: Time.at(subscription['current_period_end']),
      quantity: subscription['quantity'] || 1,
      stripe_price_id: subscription['items']['data'].first['price']['id']
    )
    
    # Update plan if price changed
    plan_name = determine_plan_from_price_id(subscription_record.stripe_price_id)
    if plan_name && subscription_record.plan != plan_name
      old_plan = subscription_record.plan
      subscription_record.update!(plan: plan_name)
      
      Rails.logger.info "Plan changed from #{old_plan} to #{plan_name} for account #{subscription_record.account_id}"
      
      # Reset usage for new billing period if plan changed
      UsageTrackingJob.perform_later('reset_usage', subscription_record.account_id)
    end
    
    # Handle subscription status changes
    case subscription['status']
    when 'active'
      handle_subscription_activated(subscription_record)
    when 'canceled', 'unpaid'
      handle_subscription_deactivated(subscription_record)
    end
  end
  
  def handle_subscription_deleted(subscription)
    subscription_record = find_subscription_by_stripe_id(subscription['id'])
    return unless subscription_record
    
    Rails.logger.info "Deleting subscription #{subscription_record.id}"
    
    # Update subscription status
    subscription_record.update!(
      status: 'canceled',
      canceled_at: Time.current
    )
    
    # Downgrade to free plan
    subscription_record.update!(plan: 'free')
    
    # Reset usage for free plan limits
    UsageTrackingJob.perform_later('reset_usage', subscription_record.account_id)
    
    Rails.logger.info "Subscription canceled for account #{subscription_record.account_id}"
  end
  
  def handle_payment_succeeded(invoice)
    customer_id = invoice['customer']
    account = find_account_by_stripe_customer_id(customer_id)
    return unless account
    
    Rails.logger.info "Payment succeeded for account #{account.id}"
    
    # Update account billing information
    if account.subscription
      account.subscription.update!(
        last_payment_at: Time.current,
        payment_failed_at: nil
      )
    end
    
    # Reset usage for new billing period
    UsageTrackingJob.perform_later('reset_usage', account.id)
    
    # TODO: Send payment success email
    # UserMailer.payment_success(account, invoice).deliver_later
  end
  
  def handle_payment_failed(invoice)
    customer_id = invoice['customer']
    account = find_account_by_stripe_customer_id(customer_id)
    return unless account
    
    Rails.logger.warn "Payment failed for account #{account.id}"
    
    # Update account billing information
    if account.subscription
      account.subscription.update!(
        payment_failed_at: Time.current
      )
    end
    
    # TODO: Send payment failure email
    # UserMailer.payment_failed(account, invoice).deliver_later
    
    # Schedule account suspension if payment continues to fail
    # SuspendAccountJob.set(wait: 3.days).perform_later(account.id)
  end
  
  def handle_customer_created(customer)
    # Find account by email or other identifier
    email = customer['email']
    return unless email
    
    user = User.find_by(email: email)
    return unless user&.account
    
    Rails.logger.info "Updating Stripe customer ID for account #{user.account.id}"
    
    user.account.update!(stripe_customer_id: customer['id'])
  end
  
  def handle_customer_updated(customer)
    account = find_account_by_stripe_customer_id(customer['id'])
    return unless account
    
    Rails.logger.info "Customer updated for account #{account.id}"
    
    # Update any relevant customer information
    # This could include updating account details based on customer metadata
  end
  
  def handle_customer_deleted(customer)
    account = find_account_by_stripe_customer_id(customer['id'])
    return unless account
    
    Rails.logger.info "Customer deleted for account #{account.id}"
    
    # Clear Stripe customer ID
    account.update!(stripe_customer_id: nil)
    
    # Cancel any active subscription
    if account.subscription&.active?
      account.subscription.update!(
        status: 'canceled',
        canceled_at: Time.current,
        plan: 'free'
      )
    end
  end
  
  def handle_payment_method_attached(payment_method)
    customer_id = payment_method['customer']
    account = find_account_by_stripe_customer_id(customer_id)
    return unless account
    
    Rails.logger.info "Payment method attached for account #{account.id}"
    
    # TODO: Update account payment method information if needed
  end
  
  def handle_payment_method_detached(payment_method)
    customer_id = payment_method['customer']
    account = find_account_by_stripe_customer_id(customer_id)
    return unless account
    
    Rails.logger.info "Payment method detached for account #{account.id}"
    
    # TODO: Handle payment method removal if needed
  end
  
  def handle_subscription_activated(subscription_record)
    Rails.logger.info "Subscription activated for account #{subscription_record.account_id}"
    
    # Ensure account has proper access
    # TODO: Send subscription activated email
    # UserMailer.subscription_activated(subscription_record.account).deliver_later
  end
  
  def handle_subscription_deactivated(subscription_record)
    Rails.logger.info "Subscription deactivated for account #{subscription_record.account_id}"
    
    # Downgrade to free plan
    subscription_record.update!(plan: 'free')
    
    # TODO: Send subscription deactivated email
    # UserMailer.subscription_deactivated(subscription_record.account).deliver_later
  end
  
  # Helper methods
  
  def find_account_by_stripe_customer_id(customer_id)
    Account.find_by(stripe_customer_id: customer_id)
  end
  
  def find_subscription_by_stripe_id(subscription_id)
    Subscription.find_by(stripe_subscription_id: subscription_id)
  end
  
  def determine_plan_from_price_id(price_id)
    return 'free' if price_id.nil?
    
    STRIPE_PLANS.each do |plan_name, config|
      return plan_name if config['stripe_price_id'] == price_id
    end
    
    Rails.logger.warn "Unknown price ID: #{price_id}"
    nil
  end
end