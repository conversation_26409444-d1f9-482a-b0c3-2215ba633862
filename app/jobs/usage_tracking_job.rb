# Job for handling usage tracking and billing operations
class UsageTrackingJob < ApplicationJob
  queue_as :default
  
  # Refresh usage for a specific account
  def perform(action, account_id, options = {})
    account = Account.find(account_id)
    
    case action.to_s
    when 'refresh_usage'
      refresh_account_usage(account)
    when 'check_limits'
      check_account_limits(account)
    when 'reset_usage'
      reset_account_usage(account)
    when 'send_usage_alert'
      send_usage_alert(account, options)
    when 'sync_subscription'
      sync_subscription_with_stripe(account)
    else
      Rails.logger.error "Unknown usage tracking action: #{action}"
    end
  rescue ActiveRecord::RecordNotFound
    Rails.logger.error "Account not found: #{account_id}"
  rescue => e
    Rails.logger.error "Error in UsageTrackingJob: #{e.message}"
    raise e
  end
  
  private
  
  def refresh_account_usage(account)
    Rails.logger.info "Refreshing usage for account #{account.id}"
    
    # Use the UsageTrackingService to refresh usage
    usage_service = UsageTrackingService.new(account)
    usage_service.refresh_usage!
    
    # Check if account is approaching or over limits
    if usage_service.over_limits?
      send_over_limit_notification(account, usage_service.exceeded_limits)
    elsif usage_service.approaching_limits?
      send_approaching_limit_notification(account, usage_service.approaching_limits)
    end
    
    Rails.logger.info "Usage refresh completed for account #{account.id}"
  end
  
  def check_account_limits(account)
    Rails.logger.info "Checking limits for account #{account.id}"
    
    usage_service = UsageTrackingService.new(account)
    
    # Check each limit type
    limits_status = {
      contacts: usage_service.can_add_contact?,
      campaigns: usage_service.can_send_campaign?,
      templates: usage_service.can_create_template?
    }
    
    # Log any limit violations
    limits_status.each do |limit_type, can_perform|
      unless can_perform
        Rails.logger.warn "Account #{account.id} has exceeded #{limit_type} limit"
      end
    end
    
    # Send notifications if needed
    if usage_service.over_limits?
      send_over_limit_notification(account, usage_service.exceeded_limits)
    end
  end
  
  def reset_account_usage(account)
    Rails.logger.info "Resetting usage for account #{account.id} for new billing period"
    
    usage_service = UsageTrackingService.new(account)
    usage_service.reset_for_new_period!
    
    Rails.logger.info "Usage reset completed for account #{account.id}"
  end
  
  def send_usage_alert(account, options)
    alert_type = options[:alert_type]
    limits = options[:limits] || []
    
    case alert_type
    when 'approaching'
      send_approaching_limit_notification(account, limits)
    when 'exceeded'
      send_over_limit_notification(account, limits)
    when 'billing_reminder'
      send_billing_reminder(account, options[:days_until_billing])
    end
  end
  
  def sync_subscription_with_stripe(account)
    return unless account.subscription&.stripe_subscription_id
    
    Rails.logger.info "Syncing subscription with Stripe for account #{account.id}"
    
    begin
      account.subscription.sync_with_stripe!
      Rails.logger.info "Subscription sync completed for account #{account.id}"
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error syncing subscription for account #{account.id}: #{e.message}"
      # Don't re-raise Stripe errors to avoid job failures
    end
  end
  
  def send_approaching_limit_notification(account, limits)
    Rails.logger.info "Sending approaching limit notification for account #{account.id}"
    
    # TODO: Implement email notification
    # UserMailer.approaching_limit_notification(account, limits).deliver_now
    
    # For now, just log the notification
    limits.each do |limit_type|
      Rails.logger.info "Account #{account.id} is approaching #{limit_type} limit"
    end
  end
  
  def send_over_limit_notification(account, limits)
    Rails.logger.info "Sending over limit notification for account #{account.id}"
    
    # TODO: Implement email notification
    # UserMailer.over_limit_notification(account, limits).deliver_now
    
    # For now, just log the notification
    limits.each do |limit_type|
      Rails.logger.warn "Account #{account.id} has exceeded #{limit_type} limit"
    end
  end
  
  def send_billing_reminder(account, days_until_billing)
    Rails.logger.info "Sending billing reminder for account #{account.id} (#{days_until_billing} days until billing)"
    
    # TODO: Implement email notification
    # UserMailer.billing_reminder(account, days_until_billing).deliver_now
  end
end