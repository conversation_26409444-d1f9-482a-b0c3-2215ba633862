# Stripe Configuration
# This file contains plan definitions and Stripe-related settings

default: &default
  plans:
    free:
      name: "Free"
      price: 0
      stripe_price_id: null
      limits:
        contacts: 100
        campaigns_per_month: 5
        templates: 3
        storage_mb: 50
      features:
        - "Basic email templates"
        - "Contact management"
        - "Basic analytics"
        - "Email support"
    
    starter:
      name: "Starter"
      price: 29
      stripe_price_id: "price_starter_monthly" # Replace with actual Stripe price ID
      limits:
        contacts: 1000
        campaigns_per_month: 50
        templates: 25
        storage_mb: 500
      features:
        - "All Free features"
        - "Advanced templates"
        - "A/B testing"
        - "Automation workflows"
        - "Priority support"
    
    professional:
      name: "Professional"
      price: 79
      stripe_price_id: "price_professional_monthly" # Replace with actual Stripe price ID
      limits:
        contacts: 10000
        campaigns_per_month: 200
        templates: 100
        storage_mb: 2000
      features:
        - "All Starter features"
        - "Advanced analytics"
        - "Custom branding"
        - "API access"
        - "Phone support"
    
    enterprise:
      name: "Enterprise"
      price: 199
      stripe_price_id: "price_enterprise_monthly" # Replace with actual Stripe price ID
      limits:
        contacts: 50000
        campaigns_per_month: 1000
        templates: 500
        storage_mb: 10000
      features:
        - "All Professional features"
        - "Unlimited automation"
        - "Dedicated account manager"
        - "Custom integrations"
        - "24/7 priority support"
        - "White-label options"
  
  # Stripe webhook events to handle
  webhook_events:
    - "customer.subscription.created"
    - "customer.subscription.updated"
    - "customer.subscription.deleted"
    - "invoice.payment_succeeded"
    - "invoice.payment_failed"
    - "customer.created"
    - "customer.updated"
    - "customer.deleted"
    - "payment_method.attached"
    - "payment_method.detached"
  
  # Usage tracking settings
  usage_tracking:
    refresh_interval_hours: 24
    warning_threshold_percentage: 80
    limit_threshold_percentage: 95
    grace_period_days: 3
  
  # Billing settings
  billing:
    trial_period_days: 14
    grace_period_days: 3
    invoice_reminder_days: [7, 3, 1]
    auto_downgrade_on_failure: true
    downgrade_to_plan: "free"

development:
  <<: *default
  # Override with test Stripe price IDs for development
  plans:
    free:
      name: "Free"
      price: 0
      stripe_price_id: null
      limits:
        contacts: 100
        campaigns_per_month: 5
        templates: 3
        storage_mb: 50
      features:
        - "Basic email templates"
        - "Contact management"
        - "Basic analytics"
        - "Email support"
    
    starter:
      name: "Starter"
      price: 29
      stripe_price_id: "price_test_starter" # Test price ID
      limits:
        contacts: 1000
        campaigns_per_month: 50
        templates: 25
        storage_mb: 500
      features:
        - "All Free features"
        - "Advanced templates"
        - "A/B testing"
        - "Automation workflows"
        - "Priority support"
    
    professional:
      name: "Professional"
      price: 79
      stripe_price_id: "price_test_professional" # Test price ID
      limits:
        contacts: 10000
        campaigns_per_month: 200
        templates: 100
        storage_mb: 2000
      features:
        - "All Starter features"
        - "Advanced analytics"
        - "Custom branding"
        - "API access"
        - "Phone support"
    
    enterprise:
      name: "Enterprise"
      price: 199
      stripe_price_id: "price_test_enterprise" # Test price ID
      limits:
        contacts: 50000
        campaigns_per_month: 1000
        templates: 500
        storage_mb: 10000
      features:
        - "All Professional features"
        - "Unlimited automation"
        - "Dedicated account manager"
        - "Custom integrations"
        - "24/7 priority support"
        - "White-label options"

test:
  <<: *default
  # Use the same test configuration as development
  plans:
    free:
      name: "Free"
      price: 0
      stripe_price_id: null
      limits:
        contacts: 10 # Smaller limits for testing
        campaigns_per_month: 2
        templates: 1
        storage_mb: 5
      features:
        - "Basic email templates"
    
    starter:
      name: "Starter"
      price: 29
      stripe_price_id: "price_test_starter"
      limits:
        contacts: 50
        campaigns_per_month: 10
        templates: 5
        storage_mb: 25
      features:
        - "All Free features"
        - "Advanced templates"

production:
  <<: *default
  # Production will use the actual Stripe price IDs
  # Make sure to update the stripe_price_id values with real ones from Stripe Dashboard