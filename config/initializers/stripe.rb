# frozen_string_literal: true

# Stripe Configuration
# This initializer sets up <PERSON><PERSON> with the appropriate API keys and configuration

require 'stripe'

# Set Stripe API key from Rails credentials
# Add your Stripe keys to credentials with:
# rails credentials:edit
# stripe:
#   publishable_key: pk_test_...
#   secret_key: sk_test_...
#   webhook_secret: whsec_...

Stripe.api_key = Rails.application.credentials.dig(:stripe, :secret_key)

# Set API version for consistency
Stripe.api_version = '2023-10-16'

# Configure Stripe in different environments
case Rails.env
when 'development', 'test'
  # Use test keys in development and test
  Stripe.api_key = Rails.application.credentials.dig(:stripe, :secret_key) || ENV['STRIPE_SECRET_KEY']
when 'production'
  # Use live keys in production
  Stripe.api_key = Rails.application.credentials.dig(:stripe, :secret_key)
  
  # Ensure we have the required keys in production
  if Stripe.api_key.blank?
    raise 'Stripe secret key is required in production. Please set it in Rails credentials.'
  end
end

# Log Stripe configuration (without exposing keys)
Rails.logger.info "Stripe initialized with API version #{Stripe.api_version}"
Rails.logger.info "Stripe mode: #{Stripe.api_key&.start_with?('sk_live') ? 'live' : 'test'}"

# Load Stripe configuration from YAML
STRIPE_CONFIG = Rails.application.config_for(:stripe)

# Make plans easily accessible
STRIPE_PLANS = STRIPE_CONFIG['plans']

# Webhook configuration
STRIPE_WEBHOOK_EVENTS = STRIPE_CONFIG['webhook_events']

# Usage tracking configuration
USAGE_TRACKING_CONFIG = STRIPE_CONFIG['usage_tracking']

# Billing configuration
BILLING_CONFIG = STRIPE_CONFIG['billing']

# Helper methods for plan management
module StripePlanHelper
  def self.plan_exists?(plan_name)
    STRIPE_PLANS.key?(plan_name.to_s)
  end
  
  def self.plan_config(plan_name)
    STRIPE_PLANS[plan_name.to_s]
  end
  
  def self.all_plans
    STRIPE_PLANS
  end
  
  def self.paid_plans
    STRIPE_PLANS.select { |_, config| config['price'] > 0 }
  end
  
  def self.free_plan
    STRIPE_PLANS.find { |_, config| config['price'] == 0 }&.first
  end
  
  def self.plan_price(plan_name)
    plan_config(plan_name)&.dig('price') || 0
  end
  
  def self.plan_limits(plan_name)
    plan_config(plan_name)&.dig('limits') || {}
  end
  
  def self.plan_features(plan_name)
    plan_config(plan_name)&.dig('features') || []
  end
  
  def self.stripe_price_id(plan_name)
    plan_config(plan_name)&.dig('stripe_price_id')
  end
  
  def self.can_upgrade?(from_plan, to_plan)
    from_price = plan_price(from_plan)
    to_price = plan_price(to_plan)
    to_price > from_price
  end
  
  def self.can_downgrade?(from_plan, to_plan)
    from_price = plan_price(from_plan)
    to_price = plan_price(to_plan)
    to_price < from_price
  end
  
  def self.plan_order
    %w[free starter professional enterprise]
  end
  
  def self.next_plan(current_plan)
    current_index = plan_order.index(current_plan.to_s)
    return nil if current_index.nil? || current_index >= plan_order.length - 1
    
    plan_order[current_index + 1]
  end
  
  def self.previous_plan(current_plan)
    current_index = plan_order.index(current_plan.to_s)
    return nil if current_index.nil? || current_index <= 0
    
    plan_order[current_index - 1]
  end
end

# Add helper methods to global scope for easy access
Rails.application.config.to_prepare do
  # Make StripePlanHelper methods available globally
  Object.include(Module.new do
    def stripe_plan_config(plan_name)
      StripePlanHelper.plan_config(plan_name)
    end
    
    def stripe_plans
      StripePlanHelper.all_plans
    end
    
    def stripe_paid_plans
      StripePlanHelper.paid_plans
    end
  end)
end

# Validate configuration on startup
begin
  # Validate plan configuration
  STRIPE_PLANS.each do |plan_name, config|
    required_keys = %w[name price limits features]
    missing_keys = required_keys - config.keys
    
    if missing_keys.any?
      Rails.logger.error "Plan '#{plan_name}' is missing required keys: #{missing_keys.join(', ')}"
    end
    
    # Validate limits structure
    if config['limits']
      required_limits = %w[contacts campaigns_per_month templates storage_mb]
      missing_limits = required_limits - config['limits'].keys
      
      if missing_limits.any?
        Rails.logger.warn "Plan '#{plan_name}' is missing limit keys: #{missing_limits.join(', ')}"
      end
    end
  end
  
  Rails.logger.info "Stripe configuration loaded successfully with #{STRIPE_PLANS.keys.length} plans"
rescue => e
  Rails.logger.error "Error loading Stripe configuration: #{e.message}"
end