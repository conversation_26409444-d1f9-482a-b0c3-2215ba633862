# Stripe-related routes
# This file should be included in the main routes.rb file

# Subscription management
resource :subscription, only: [:show, :create] do
  member do
    patch :change_plan
    patch :cancel
    patch :reactivate
    patch :update_payment_method
    get :usage
    post :refresh_usage
  end
end

# Billing management
resources :billing, only: [:index] do
  member do
    post :create_portal_session
  end
  
  collection do
    get :invoices
    get 'invoices/:invoice_id', to: 'billing#invoice', as: :invoice
    get :payment_methods
    post :payment_methods, to: 'billing#create_payment_method'
    delete 'payment_methods/:payment_method_id', to: 'billing#destroy_payment_method', as: :destroy_payment_method
    patch 'payment_methods/:payment_method_id/set_default', to: 'billing#set_default_payment_method', as: :set_default_payment_method
    get :upcoming_invoice
  end
end

# Stripe webhooks
post '/stripe/webhooks', to: 'stripe_webhooks#create'

# API endpoints for frontend usage
namespace :api do
  namespace :v1 do
    resources :usage, only: [:index] do
      collection do
        post :refresh
      end
    end
    
    resources :subscriptions, only: [:show, :create, :update] do
      member do
        patch :change_plan
        patch :cancel
        patch :reactivate
      end
    end
    
    resources :billing, only: [:index] do
      collection do
        get :invoices
        get :payment_methods
        post :payment_methods
        delete 'payment_methods/:payment_method_id', to: 'billing#destroy_payment_method'
        patch 'payment_methods/:payment_method_id/set_default', to: 'billing#set_default_payment_method'
      end
    end
  end
end